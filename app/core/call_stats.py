#!/usr/bin/env python3
"""
API调用统计模块
用于统计接口调用次数，存储到Redis中
"""
import redis
from functools import wraps
import logging
from typing import Dict, Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

class CallStatsManager:
    """API调用统计管理器（非阻塞版本）"""
    
    def __init__(self):
        self.redis_client = self._connect_redis()
        # 增加线程池大小以支持高并发
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="redis_stats")
        self._lock = threading.Lock()
    
    def _connect_redis(self):
        """连接Redis"""
        try:
            redis_client = redis.Redis(
                host="r-bp18f2292281c704.redis.rds.aliyuncs.com",
                port=6379,
                password="secret#123456#",
                decode_responses=True,  # 开启自动解码，便于处理
                socket_connect_timeout=1,  # 降低连接超时
                socket_timeout=1,  # 降低操作超时
                socket_keepalive=True,
                health_check_interval=30
            )
            
            # 延迟测试连接，不在初始化时阻塞
            logger.info("Redis客户端已创建，将在首次使用时验证连接")
            return redis_client
        except Exception as e:
            logger.error(f"❌ Redis客户端创建失败: {e}")
            return None
    
    def increment_call(self, method_name: str) -> int:
        """
        增加方法调用计数（异步非阻塞）
        
        Args:
            method_name (str): 方法名
            
        Returns:
            int: 始终返回0，不阻塞主流程
        """
        if not self.redis_client:
            return 0
        
        # 提交到线程池异步执行，不等待结果
        self.executor.submit(self._increment_call_async, method_name)
        return 0
    
    def _increment_call_async(self, method_name: str):
        """异步执行 Redis 增量操作"""
        try:
            self.redis_client.incr(method_name)
            logger.debug(f"📊 {method_name} 统计已更新")
        except redis.TimeoutError:
            logger.debug(f"Redis超时，跳过统计 {method_name}")
        except redis.ConnectionError:
            logger.debug(f"Redis连接失败，跳过统计 {method_name}")
        except Exception as e:
            logger.debug(f"统计失败 {method_name}: {e}")
    
    def get_call_count(self, method_name: str) -> int:
        """
        获取方法调用次数（非阻塞）
        
        Args:
            method_name (str): 方法名
            
        Returns:
            int: 调用次数
        """
        if not self.redis_client:
            return 0
        
        try:
            count = self.redis_client.get(method_name)
            return int(count) if count else 0
        except redis.TimeoutError:
            logger.warning(f"⏱️ Redis超时，返回0 {method_name}")
            return 0
        except redis.ConnectionError:
            logger.warning(f"🔌 Redis连接失败，返回0 {method_name}")
            return 0
        except Exception as e:
            logger.warning(f"获取调用次数失败 {method_name}: {e}")
            return 0
    
    def get_all_stats(self) -> Dict[str, int]:
        """
        获取所有方法的调用统计
        
        Returns:
            Dict[str, int]: 方法名和调用次数的字典
        """
        if not self.redis_client:
            return {}
        
        try:
            # 预定义的方法名列表（基于controller中的endpoint方法）
            predefined_methods = [
                # intention_controller
                "create_intention_verification_endpoint",
                "query_intention_status_endpoint", 
                "confirm_intention_endpoint",
                "cancel_intention_endpoint",
                "get_intention_record_endpoint",
                # certificate_controller
                "get_test_account_endpoint",
                "create_certificate_endpoint",
                "query_certificate_detail_endpoint",
                "revoke_certificate_endpoint",
                "update_certificate_endpoint",
                "get_user_info_endpoint",
                # identity_controller
                "create_identity_verification_endpoint",
                "query_verification_status_endpoint",
                # saas_controller
                "register_test_person_account_endpoint",
                "register_test_company_account_endpoint",
                "create_organization_endpoint",
                "add_member_to_org_endpoint",
                "query_org_info_endpoint",
                "batch_open_vip_endpoint",
                "cancel_present_vip_endpoint",
                # signing_controller
                "create_signing_flow_endpoint",
                "add_signer_endpoint",
                "start_signing_endpoint",
                "query_signing_status_endpoint",
                "cancel_signing_endpoint",
                "create_flow_one_step_endpoint",
                "create_flow_by_file_endpoint",
                "one_click_sign_endpoint",
                # platform_controller
                "generate_mcp_code_endpoint",
                "get_testcase_generation_prompt_endpoint",
                "get_httprunner_automation_prompt_endpoint",
                "get_simple_testcase_prompt_endpoint",
                "get_version_info_endpoint",
                "get_upload_info_endpoint",
                "convert_to_xmind_endpoint",
                "upload_xmind_file_endpoint",
                "convert_markdown_content_endpoint",
                # fee_controller
                "add_app_quota_endpoint",
                "recharge_quota_endpoint",
                # wiki_controller
                "download_image",
                "access_link",
                "wiki_search"
            ]
            
            stats = {}
            for method in predefined_methods:
                count = self.redis_client.get(method)
                stats[method] = int(count) if count else 0
            
            return stats
        except Exception as e:
            logger.error(f"❌ 获取所有统计失败: {e}")
            return {}
    
    def reset_stats(self, method_name: Optional[str] = None):
        """
        重置统计数据
        
        Args:
            method_name (str, optional): 指定方法名，如果为None则重置所有
        """
        if not self.redis_client:
            return
        
        try:
            if method_name:
                # 重置指定方法
                self.redis_client.set(method_name, 0)
                logger.info(f"🔄 已重置 {method_name} 的统计")
            else:
                # 重置所有预定义方法
                predefined_methods = [
                    # intention_controller
                    "create_intention_verification_endpoint",
                    "query_intention_status_endpoint", 
                    "confirm_intention_endpoint",
                    "cancel_intention_endpoint",
                    "get_intention_record_endpoint",
                    # certificate_controller
                    "get_test_account_endpoint",
                    "create_certificate_endpoint",
                    "query_certificate_detail_endpoint",
                    "revoke_certificate_endpoint",
                    "update_certificate_endpoint",
                    "get_user_info_endpoint",
                    # identity_controller
                    "create_identity_verification_endpoint",
                    "query_verification_status_endpoint",
                    # saas_controller
                    "register_test_person_account_endpoint",
                    "register_test_company_account_endpoint",
                    "create_organization_endpoint",
                    "add_member_to_org_endpoint",
                    "query_org_info_endpoint",
                    "batch_open_vip_endpoint",
                    "cancel_present_vip_endpoint",
                    # signing_controller
                    "create_signing_flow_endpoint",
                    "add_signer_endpoint",
                    "start_signing_endpoint",
                    "query_signing_status_endpoint",
                    "cancel_signing_endpoint",
                    "create_flow_one_step_endpoint",
                    "create_flow_by_file_endpoint",
                    "one_click_sign_endpoint",
                    # platform_controller
                    "generate_mcp_code_endpoint",
                    "get_testcase_generation_prompt_endpoint",
                    "get_httprunner_automation_prompt_endpoint",
                    "get_simple_testcase_prompt_endpoint",
                    "get_version_info_endpoint",
                    "get_upload_info_endpoint",
                    "convert_to_xmind_endpoint",
                    "upload_xmind_file_endpoint",
                    "convert_markdown_content_endpoint",
                    # fee_controller
                    "add_app_quota_endpoint",
                    "recharge_quota_endpoint",
                    # wiki_controller
                    "download_image",
                    "access_link",
                    "wiki_search"
                ]
                
                for method in predefined_methods:
                    self.redis_client.set(method, 0)
                logger.info(f"🔄 已重置所有 {len(predefined_methods)} 个方法的统计")
        except Exception as e:
            logger.error(f"❌ 重置统计失败: {e}")


def count_api_calls(func):
    """
    API调用计数装饰器
    
    使用方法：
    @count_api_calls
    async def some_endpoint(...):
        pass
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 获取方法名
        method_name = func.__name__
        
        # 增加计数
        try:
            call_stats_manager.increment_call(method_name)
        except Exception as e:
            logger.error(f"❌ 统计调用失败: {e}")
        
        # 执行原方法
        return await func(*args, **kwargs)
    
    return wrapper


# 全局实例
call_stats_manager = CallStatsManager()


# 便捷函数
def get_method_call_count(method_name: str) -> int:
    """获取指定方法的调用次数"""
    return call_stats_manager.get_call_count(method_name)


def get_all_call_stats() -> Dict[str, int]:
    """获取所有方法的调用统计"""
    return call_stats_manager.get_all_stats()


def reset_call_stats(method_name: Optional[str] = None):
    """重置调用统计"""
    call_stats_manager.reset_stats(method_name)