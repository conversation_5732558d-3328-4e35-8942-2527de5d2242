"""
方法名到中文名称的映射配置
"""

# 方法名到中文名称的映射表
METHOD_NAME_MAPPING = {
    # intention_controller - 意愿验证
    "create_intention_verification_endpoint": "创建意愿验证",
    "query_intention_status_endpoint": "查询意愿状态",
    "confirm_intention_endpoint": "确认意愿",
    "cancel_intention_endpoint": "取消意愿",
    "get_intention_record_endpoint": "获取意愿记录",
    
    # certificate_controller - 证书管理
    "get_test_account_endpoint": "获取测试账号",
    "create_certificate_endpoint": "创建证书",
    "query_certificate_detail_endpoint": "查询证书详情",
    "revoke_certificate_endpoint": "吊销证书",
    "update_certificate_endpoint": "更新证书",
    "get_user_info_endpoint": "获取用户信息",
    
    # identity_controller - 实名认证
    "create_identity_verification_endpoint": "创建实名认证",
    "query_verification_status_endpoint": "查询认证状态",
    
    # saas_controller - SaaS服务
    "register_test_person_account_endpoint": "注册测试个人账号",
    "register_test_company_account_endpoint": "注册测试企业账号",
    "create_organization_endpoint": "创建组织",
    "add_member_to_org_endpoint": "添加组织成员",
    "query_org_info_endpoint": "查询组织信息",
    "batch_open_vip_endpoint": "赠送版本会员",
    "cancel_present_vip_endpoint": "取消赠送会员版本",
    
    # signing_controller - 签署流程
    "create_signing_flow_endpoint": "创建签署流程",
    "add_signer_endpoint": "添加签署人",
    "start_signing_endpoint": "启动签署",
    "query_signing_status_endpoint": "查询签署状态",
    "cancel_signing_endpoint": "取消签署",
    "create_flow_one_step_endpoint": "一步创建签署流程",
    "create_flow_by_file_endpoint": "获取签署链接",
    "one_click_sign_endpoint": "一键签署",
    
    # platform_controller - 平台工具
    "generate_mcp_code_endpoint": "生成MCP代码",
    "get_testcase_generation_prompt_endpoint": "获取测试用例生成提示词",
    "get_httprunner_automation_prompt_endpoint": "获取集测脚本生成提示词",
    "get_simple_testcase_prompt_endpoint": "获取简版测试用例生成提示词",
    "get_version_info_endpoint": "获取版本信息",
    "get_upload_info_endpoint": "获取上传信息",
    "convert_to_xmind_endpoint": "转换为XMind",
    "upload_xmind_file_endpoint": "上传XMind文件",
    "convert_markdown_content_endpoint": "转换Markdown内容",
    "upload_file_to_tsign_endpoint": "上传文件到公有云",
    "list_upload_files_endpoint": "列出上传目录文件",
    
    # fee_controller - 计费充值
    "add_app_quota_endpoint": "充值应用数量",
    "recharge_quota_endpoint": "充值账户余额",
    
    # file_controller - 文件管理
    "get_upload_url_endpoint": "获取文件id",
    
    # wiki_controller - Wiki服务
    "download_image": "下载图片",
    "access_link": "访问链接",
    "wiki_search": "Wiki搜索",
    
    # knowledge_controller - 知识库
    "query_knowledge_endpoint": "查询知识库",
    "write_knowledge_endpoint": "写入知识库",
    "write_single_knowledge_endpoint": "写入单个文档",
    
    # device_management - 设备管理
    "device_management_add_device": "添加设备",
    "device_management_list_devices": "获取设备列表",
    "device_management_update_holder": "更新设备持有人",
    "device_management_get_history": "获取设备流转历史",
}


def get_chinese_name(method_name: str) -> str:
    """
    获取方法的中文名称
    
    Args:
        method_name: 方法名（英文）
        
    Returns:
        str: 中文名称，如果没有映射则返回原方法名
    """
    return METHOD_NAME_MAPPING.get(method_name, method_name)


def get_all_mappings() -> dict:
    """
    获取所有映射关系
    
    Returns:
        dict: 完整的映射字典
    """
    return METHOD_NAME_MAPPING.copy()
