"""
API控制器
"""
from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel, Field, field_validator
from typing import List
from .database import get_db
from .service import DeviceManagementService
from app.core.mcp_decorator import mcp_endpoint
import logging
import os

logger = logging.getLogger(__name__)

# 创建路由器，配置路由前缀和标签
router = APIRouter(
    prefix="/device-management",
    tags=["设备管理"]
)


class AddDeviceRequest(BaseModel):
    """添加设备请求模型"""
    device_model: str = Field(..., min_length=1, max_length=100, description="手机型号")
    holder: str = Field(..., min_length=1, max_length=100, description="持有人姓名")
    manager: str = Field(None, max_length=100, description="负责人姓名")
    system_version: str = Field(None, max_length=100, description="系统版本，如：鸿蒙3.0、HarmonyOS NEXT 0.0.71")
    remark: str = Field(None, max_length=500, description="手机备注，如：AppleId账号、报废等")
    
    @field_validator('device_model', 'holder')
    def validate_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('字段不能为空或只包含空格')
        return v.strip()
    
    @field_validator('manager', 'system_version', 'remark')
    def validate_optional_fields(cls, v):
        if v is not None and v.strip():
            return v.strip()
        return None


class UpdateHolderRequest(BaseModel):
    """更新持有人请求模型"""
    device_model: str = Field(..., min_length=1, max_length=100, description="手机型号")
    holder: str = Field(..., min_length=1, max_length=100, description="新持有人姓名")
    manager: str = Field(None, max_length=100, description="负责人姓名")
    system_version: str = Field(None, max_length=100, description="系统版本，如：鸿蒙3.0、HarmonyOS NEXT 0.0.71")
    remark: str = Field(None, max_length=500, description="手机备注，如：AppleId账号、报废等")
    
    @field_validator('device_model', 'holder')
    def validate_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('字段不能为空或只包含空格')
        return v.strip()
    
    @field_validator('manager', 'system_version', 'remark')
    def validate_optional_fields(cls, v):
        if v is not None and v.strip():
            return v.strip()
        return None


@router.get("/", response_class=HTMLResponse)
async def get_page():
    """
    获取设备管理主页面
    
    Returns:
        HTMLResponse: 返回设备管理系统的HTML页面
    """
    try:
        logger.info("请求设备管理主页面")
        
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 构建HTML文件路径
        html_file_path = os.path.join(current_dir, "templates", "index.html")
        
        # 读取HTML文件内容
        with open(html_file_path, "r", encoding="utf-8") as f:
            html_content = f.read()
        
        logger.info("设备管理主页面加载成功")
        return HTMLResponse(content=html_content, status_code=200)
        
    except FileNotFoundError:
        logger.error(f"HTML文件不存在: {html_file_path}")
        return HTMLResponse(
            content="<h1>页面未找到</h1><p>HTML模板文件不存在</p>",
            status_code=404
        )
    except PermissionError as e:
        logger.error(f"读取HTML文件权限不足: {str(e)}")
        return HTMLResponse(
            content="<h1>服务器错误</h1><p>无法读取页面文件，权限不足</p>",
            status_code=500
        )
    except Exception as e:
        logger.error(f"读取HTML文件失败 - 未知错误: {str(e)}", exc_info=True)
        return HTMLResponse(
            content=f"<h1>服务器错误</h1><p>无法加载页面: {str(e)}</p>",
            status_code=500
        )


@router.post(
    "/add",
    summary="添加新设备记录",
    description="添加新的测试设备记录，自动生成流转时间",
    operation_id="device_management_add_device"
)
@mcp_endpoint
async def add_device(
    request: AddDeviceRequest,
    db: Session = Depends(get_db)
):
    """
    添加新设备记录
    
    Args:
        request: 添加设备请求对象
        db: 数据库会话
        
    Returns:
        统一格式响应，包含新创建的记录
    """
    try:
        logger.info(f"API请求 - 添加设备记录: device_model={request.device_model}, holder={request.holder}")
        
        # 调用Service层方法
        result = await DeviceManagementService.add_device_record(
            device_model=request.device_model,
            holder=request.holder,
            db=db,
            manager=request.manager,
            system_version=request.system_version,
            remark=request.remark
        )
        
        logger.info(f"API请求成功 - 添加设备记录: id={result.get('id')}")
        return {
            "success": True,
            "message": "添加成功",
            "data": result
        }
        
    except ValueError as e:
        logger.warning(f"API请求失败 - 参数验证错误: {str(e)}")
        return {
            "success": False,
            "message": f"参数验证失败: {str(e)}",
            "error_code": "VALIDATION_ERROR",
            "data": None
        }
    except SQLAlchemyError as e:
        logger.error(f"API请求失败 - 数据库错误: {str(e)}")
        return {
            "success": False,
            "message": "数据库操作失败，请稍后重试",
            "error_code": "DATABASE_ERROR",
            "data": None
        }
    except Exception as e:
        logger.error(f"API请求失败 - 未知错误: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"系统错误: {str(e)}",
            "error_code": "SYSTEM_ERROR",
            "data": None
        }


@router.get(
    "/list",
    summary="获取设备列表",
    description="获取所有设备的当前状态，每个设备只显示最新记录",
    operation_id="device_management_list_devices"
)
@mcp_endpoint
async def list_devices(db: Session = Depends(get_db)):
    """
    获取所有设备的当前状态
    
    Args:
        db: 数据库会话
        
    Returns:
        统一格式响应，包含设备列表
    """
    try:
        logger.info("API请求 - 获取设备列表")
        
        # 调用Service层方法获取当前设备列表
        devices = await DeviceManagementService.get_current_devices(db)
        
        logger.info(f"API请求成功 - 获取设备列表: 共 {len(devices)} 条记录")
        return {
            "success": True,
            "message": f"获取成功，共 {len(devices)} 条记录",
            "data": devices
        }
        
    except SQLAlchemyError as e:
        logger.error(f"API请求失败 - 数据库错误: {str(e)}")
        return {
            "success": False,
            "message": "数据库操作失败，请稍后重试",
            "error_code": "DATABASE_ERROR",
            "data": []
        }
    except Exception as e:
        logger.error(f"API请求失败 - 未知错误: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"系统错误: {str(e)}",
            "error_code": "SYSTEM_ERROR",
            "data": []
        }


@router.put(
    "/update",
    summary="更新设备持有人",
    description="更新设备持有人，创建新的流转记录",
    operation_id="device_management_update_holder"
)
@mcp_endpoint
async def update_holder(
    request: UpdateHolderRequest,
    db: Session = Depends(get_db)
):
    """
    更新设备持有人
    
    Args:
        request: 更新持有人请求对象
        db: 数据库会话
        
    Returns:
        统一格式响应，包含新创建的流转记录
    """
    try:
        logger.info(f"API请求 - 更新设备持有人: device_model={request.device_model}, holder={request.holder}")
        
        # 调用Service层方法
        result = await DeviceManagementService.update_device_holder(
            device_model=request.device_model,
            new_holder=request.holder,
            db=db,
            manager=request.manager,
            system_version=request.system_version,
            remark=request.remark
        )
        
        logger.info(f"API请求成功 - 更新设备持有人: id={result.get('id')}")
        return {
            "success": True,
            "message": "持有人更新成功",
            "data": result
        }
        
    except ValueError as e:
        logger.warning(f"API请求失败 - 参数验证错误: {str(e)}")
        return {
            "success": False,
            "message": f"参数验证失败: {str(e)}",
            "error_code": "VALIDATION_ERROR",
            "data": None
        }
    except SQLAlchemyError as e:
        logger.error(f"API请求失败 - 数据库错误: {str(e)}")
        return {
            "success": False,
            "message": "数据库操作失败，请稍后重试",
            "error_code": "DATABASE_ERROR",
            "data": None
        }
    except Exception as e:
        logger.error(f"API请求失败 - 未知错误: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"系统错误: {str(e)}",
            "error_code": "SYSTEM_ERROR",
            "data": None
        }


@router.get(
    "/history/{device_model}",
    summary="获取设备流转历史",
    description="获取指定设备的所有流转历史记录",
    operation_id="device_management_get_history"
)
@mcp_endpoint
async def get_history(
    device_model: str,
    db: Session = Depends(get_db)
):
    """
    获取设备流转历史
    
    Args:
        device_model: 手机型号（路径参数）
        db: 数据库会话
        
    Returns:
        统一格式响应，包含历史记录列表
    """
    try:
        logger.info(f"API请求 - 获取设备流转历史: device_model={device_model}")
        
        # 参数验证
        if not device_model or not device_model.strip():
            logger.warning("API请求失败 - 手机型号为空")
            return {
                "success": False,
                "message": "手机型号不能为空",
                "error_code": "INVALID_DEVICE_MODEL",
                "data": []
            }
        
        # 调用Service层方法
        history = await DeviceManagementService.get_device_history(
            device_model=device_model.strip(),
            db=db
        )
        
        logger.info(f"API请求成功 - 获取设备流转历史: device_model={device_model}, 共 {len(history)} 条记录")
        return {
            "success": True,
            "message": f"获取成功，共 {len(history)} 条历史记录",
            "data": history
        }
        
    except ValueError as e:
        logger.warning(f"API请求失败 - 参数验证错误: {str(e)}")
        return {
            "success": False,
            "message": f"参数验证失败: {str(e)}",
            "error_code": "VALIDATION_ERROR",
            "data": []
        }
    except SQLAlchemyError as e:
        logger.error(f"API请求失败 - 数据库错误: {str(e)}")
        return {
            "success": False,
            "message": "数据库操作失败，请稍后重试",
            "error_code": "DATABASE_ERROR",
            "data": []
        }
    except Exception as e:
        logger.error(f"API请求失败 - 未知错误: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"系统错误: {str(e)}",
            "error_code": "SYSTEM_ERROR",
            "data": []
        }
