"""
数据库连接配置
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 数据库连接配置
DATABASE_URL = "mysql+pymysql://root:root123@192.168.2.137:3306/e2e?charset=utf8mb4"

# 创建数据库引擎（优化并发性能）
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,      # 连接池预检查
    pool_recycle=3600,       # 连接回收时间（1小时）
    pool_size=20,            # 连接池大小（增加以支持并发）
    max_overflow=30,         # 最大溢出连接数（总共最多50个连接）
    pool_timeout=3,          # 获取连接超时（秒）- 降低避免长时间等待
    echo=False,              # 不打印SQL语句
    pool_use_lifo=True,      # 使用LIFO策略，提高连接复用
    connect_args={
        "charset": "utf8mb4",
        "connect_timeout": 2,    # MySQL 连接超时（秒）- 降低
        "read_timeout": 10,      # 读取超时（秒）
        "write_timeout": 10      # 写入超时（秒）
    }
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 声明基类
Base = declarative_base()


def get_db():
    """
    数据库会话依赖注入函数
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """
    初始化数据库，创建所有表
    """
    from . import models  # 导入模型以注册表
    Base.metadata.create_all(bind=engine)
