"""
数据模型定义
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Index
from .database import Base


class DeviceRecord(Base):
    """
    设备流转记录模型
    """
    __tablename__ = "test_device_records"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    device_model = Column(String(100), nullable=False, comment="手机型号")
    holder = Column(String(100), nullable=False, comment="持有人")
    manager = Column(String(100), nullable=True, comment="负责人")
    transfer_time = Column(DateTime, nullable=False, default=datetime.now, comment="流转时间")
    system_version = Column(String(100), nullable=True, comment="系统版本")
    remark = Column(String(500), nullable=True, comment="手机备注")
    
    # 创建索引和表配置
    __table_args__ = (
        Index('idx_device_model', 'device_model'),
        {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )
    
    def to_dict(self):
        """
        转换为字典格式
        """
        return {
            "id": self.id,
            "device_model": self.device_model,
            "holder": self.holder,
            "manager": self.manager,
            "transfer_time": self.transfer_time.strftime("%Y-%m-%d %H:%M:%S") if self.transfer_time else None,
            "system_version": self.system_version,
            "remark": self.remark
        }
