"""
业务逻辑层
"""
from typing import List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
from .models import DeviceRecord
import logging

logger = logging.getLogger(__name__)


class DeviceManagementService:
    """
    设备管理服务类
    """
    
    @staticmethod
    async def add_device_record(
        device_model: str, 
        holder: str, 
        db: Session,
        manager: str = None,
        system_version: str = None,
        remark: str = None
    ) -> dict:
        """
        添加新设备记录
        
        Args:
            device_model: 手机型号
            holder: 持有人
            db: 数据库会话
            system_version: 系统版本（可选）
            remark: 手机备注（可选）
            
        Returns:
            dict: 新创建的记录
            
        Raises:
            ValueError: 参数验证失败
            SQLAlchemyError: 数据库操作失败
        """
        try:
            # 参数验证
            if not device_model or len(device_model) > 100:
                logger.warning(f"参数验证失败: device_model='{device_model}'")
                raise ValueError("手机型号不能为空且长度不能超过100个字符")
            
            if not holder or len(holder) > 100:
                logger.warning(f"参数验证失败: holder='{holder}'")
                raise ValueError("持有人不能为空且长度不能超过100个字符")
            
            if system_version and len(system_version) > 100:
                logger.warning(f"参数验证失败: system_version过长")
                raise ValueError("系统版本长度不能超过100个字符")
            
            if remark and len(remark) > 500:
                logger.warning(f"参数验证失败: remark过长")
                raise ValueError("手机备注长度不能超过500个字符")
            
            logger.info(f"开始添加设备记录: device_model={device_model}, holder={holder}, system_version={system_version}")
            
            # 创建新的设备记录，transfer_time会自动生成
            new_record = DeviceRecord(
                device_model=device_model,
                holder=holder,
                manager=manager,
                transfer_time=datetime.now(),
                system_version=system_version,
                remark=remark
            )
            
            # 保存到数据库
            db.add(new_record)
            db.commit()
            db.refresh(new_record)
            
            logger.info(f"添加设备记录成功: id={new_record.id}, device_model={device_model}, holder={holder}")
            return new_record.to_dict()
            
        except ValueError as e:
            # 参数验证错误，不需要回滚
            logger.error(f"添加设备记录失败 - 参数验证错误: {str(e)}")
            raise
        except IntegrityError as e:
            db.rollback()
            logger.error(f"添加设备记录失败 - 数据完整性错误: device_model={device_model}, holder={holder}, error={str(e)}")
            raise SQLAlchemyError(f"数据完整性错误: {str(e)}")
        except OperationalError as e:
            db.rollback()
            logger.error(f"添加设备记录失败 - 数据库连接错误: {str(e)}")
            raise SQLAlchemyError("数据库连接失败，请稍后重试")
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"添加设备记录失败 - 数据库错误: device_model={device_model}, holder={holder}, error={str(e)}")
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"添加设备记录失败 - 未知错误: device_model={device_model}, holder={holder}, error={str(e)}", exc_info=True)
            raise
    
    @staticmethod
    async def get_current_devices(db: Session) -> List[dict]:
        """
        获取所有设备的当前状态（每个设备只返回最新记录）
        
        Args:
            db: 数据库会话
            
        Returns:
            List[dict]: 设备列表
            
        Raises:
            SQLAlchemyError: 数据库操作失败
        """
        try:
            logger.info("开始获取当前设备列表")
            
            # 使用子查询获取每个设备的最新记录
            # 子查询：获取每个device_model的最大transfer_time
            subquery = db.query(
                DeviceRecord.device_model,
                func.max(DeviceRecord.transfer_time).label('max_time')
            ).group_by(DeviceRecord.device_model).subquery()
            
            # 主查询：根据device_model和max_time获取完整记录
            current_devices = db.query(DeviceRecord).join(
                subquery,
                (DeviceRecord.device_model == subquery.c.device_model) &
                (DeviceRecord.transfer_time == subquery.c.max_time)
            ).order_by(DeviceRecord.transfer_time.desc()).all()
            
            logger.info(f"获取当前设备列表成功，共 {len(current_devices)} 条记录")
            return [device.to_dict() for device in current_devices]
            
        except OperationalError as e:
            logger.error(f"获取当前设备列表失败 - 数据库连接错误: {str(e)}")
            raise SQLAlchemyError("数据库连接失败，请稍后重试")
        except SQLAlchemyError as e:
            logger.error(f"获取当前设备列表失败 - 数据库错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取当前设备列表失败 - 未知错误: {str(e)}", exc_info=True)
            raise
    
    @staticmethod
    async def update_device_holder(
        device_model: str, 
        new_holder: str, 
        db: Session,
        manager: str = None,
        system_version: str = None,
        remark: str = None
    ) -> dict:
        """
        更新设备持有人（创建新流转记录）
        
        Args:
            device_model: 手机型号
            new_holder: 新持有人
            db: 数据库会话
            system_version: 系统版本（可选）
            remark: 手机备注（可选）
            
        Returns:
            dict: 新创建的记录
            
        Raises:
            ValueError: 参数验证失败
            SQLAlchemyError: 数据库操作失败
        """
        try:
            # 参数验证
            if not device_model or len(device_model) > 100:
                logger.warning(f"参数验证失败: device_model='{device_model}'")
                raise ValueError("手机型号不能为空且长度不能超过100个字符")
            
            if not new_holder or len(new_holder) > 100:
                logger.warning(f"参数验证失败: new_holder='{new_holder}'")
                raise ValueError("持有人不能为空且长度不能超过100个字符")
            
            if system_version and len(system_version) > 100:
                logger.warning(f"参数验证失败: system_version过长")
                raise ValueError("系统版本长度不能超过100个字符")
            
            if remark and len(remark) > 500:
                logger.warning(f"参数验证失败: remark过长")
                raise ValueError("手机备注长度不能超过500个字符")
            
            logger.info(f"开始更新设备持有人: device_model={device_model}, new_holder={new_holder}")
            
            # 获取该设备的最新记录，继承系统版本、备注和负责人（如果没有提供新值）
            if system_version is None or remark is None or manager is None:
                latest_record = db.query(DeviceRecord).filter(
                    DeviceRecord.device_model == device_model
                ).order_by(DeviceRecord.transfer_time.desc()).first()
                
                if latest_record:
                    if system_version is None:
                        system_version = latest_record.system_version
                    if remark is None:
                        remark = latest_record.remark
                    if manager is None:
                        manager = latest_record.manager
            
            # 创建新的流转记录（保留机型，新持有人，新时间）
            new_record = DeviceRecord(
                device_model=device_model,
                holder=new_holder,
                manager=manager,
                transfer_time=datetime.now(),
                system_version=system_version,
                remark=remark
            )
            
            # 保存到数据库
            db.add(new_record)
            db.commit()
            db.refresh(new_record)
            
            logger.info(f"更新设备持有人成功: id={new_record.id}, device_model={device_model}, new_holder={new_holder}")
            return new_record.to_dict()
            
        except ValueError as e:
            # 参数验证错误，不需要回滚
            logger.error(f"更新设备持有人失败 - 参数验证错误: {str(e)}")
            raise
        except IntegrityError as e:
            db.rollback()
            logger.error(f"更新设备持有人失败 - 数据完整性错误: device_model={device_model}, new_holder={new_holder}, error={str(e)}")
            raise SQLAlchemyError(f"数据完整性错误: {str(e)}")
        except OperationalError as e:
            db.rollback()
            logger.error(f"更新设备持有人失败 - 数据库连接错误: {str(e)}")
            raise SQLAlchemyError("数据库连接失败，请稍后重试")
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"更新设备持有人失败 - 数据库错误: device_model={device_model}, new_holder={new_holder}, error={str(e)}")
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"更新设备持有人失败 - 未知错误: device_model={device_model}, new_holder={new_holder}, error={str(e)}", exc_info=True)
            raise
    
    @staticmethod
    async def get_device_history(device_model: str, db: Session) -> List[dict]:
        """
        获取指定设备的所有流转历史
        
        Args:
            device_model: 手机型号
            db: 数据库会话
            
        Returns:
            List[dict]: 历史记录列表
            
        Raises:
            ValueError: 参数验证失败
            SQLAlchemyError: 数据库操作失败
        """
        try:
            # 参数验证
            if not device_model or len(device_model) > 100:
                logger.warning(f"参数验证失败: device_model='{device_model}'")
                raise ValueError("手机型号不能为空且长度不能超过100个字符")
            
            logger.info(f"开始获取设备流转历史: device_model={device_model}")
            
            # 根据device_model查询所有记录，按时间倒序排列
            history_records = db.query(DeviceRecord).filter(
                DeviceRecord.device_model == device_model
            ).order_by(DeviceRecord.transfer_time.desc()).all()
            
            logger.info(f"获取设备流转历史成功: device_model={device_model}，共 {len(history_records)} 条记录")
            return [record.to_dict() for record in history_records]
            
        except ValueError as e:
            logger.error(f"获取设备流转历史失败 - 参数验证错误: {str(e)}")
            raise
        except OperationalError as e:
            logger.error(f"获取设备流转历史失败 - 数据库连接错误: device_model={device_model}, error={str(e)}")
            raise SQLAlchemyError("数据库连接失败，请稍后重试")
        except SQLAlchemyError as e:
            logger.error(f"获取设备流转历史失败 - 数据库错误: device_model={device_model}, error={str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取设备流转历史失败 - 未知错误: device_model={device_model}, error={str(e)}", exc_info=True)
            raise
