<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试机管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        h1 {
            font-size: 28px;
            margin-bottom: 30px;
            color: #1a1a1a;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        .toolbar {
            margin-bottom: 20px;
        }

        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #45a049;
        }

        button.secondary {
            background-color: #2196F3;
        }

        button.secondary:hover {
            background-color: #0b7dda;
        }

        button.danger {
            background-color: #f44336;
        }

        button.danger:hover {
            background-color: #da190b;
        }

        button.small {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table thead {
            background-color: #f8f9fa;
        }

        table th {
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #555;
            border-bottom: 2px solid #dee2e6;
        }

        table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }

        table tbody tr:hover {
            background-color: #f8f9fa;
        }

        table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 列宽控制 */
        table th:nth-child(1), table td:nth-child(1) { width: 15%; } /* 手机型号 */
        table th:nth-child(2), table td:nth-child(2) { width: 10%; } /* 系统版本 */
        table th:nth-child(3), table td:nth-child(3) { width: 8%; }  /* 负责人 */
        table th:nth-child(4), table td:nth-child(4) { width: 8%; }  /* 当前持有人 */
        table th:nth-child(5), table td:nth-child(5) { width: 12%; } /* 时间 */
        table th:nth-child(6), table td:nth-child(6) { 
            width: 12%; 
            max-width: 150px;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
        } /* 备注 - 限制宽度，自动换行 */
        table th:nth-child(7), table td:nth-child(7) { width: 35%; } /* 操作 */

        .empty-message {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 14px;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideDown 0.3s;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-content h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        /* 操作列样式 */
        .action-cell {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .holder-edit {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .holder-edit input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            width: 120px;
        }

        .holder-edit input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        /* 历史记录弹窗表格 */
        .history-table {
            margin-top: 20px;
        }

        .history-table table {
            margin-top: 0;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        /* 消息提示 */
        .message {
            padding: 12px 20px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: none;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试机管理系统</h1>

        <!-- 消息提示 -->
        <div id="message" class="message"></div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <button onclick="showAddModal()">新增设备</button>
        </div>

        <!-- 设备列表表格 -->
        <table id="deviceTable">
            <thead>
                <tr>
                    <th>手机型号</th>
                    <th>系统版本</th>
                    <th>负责人</th>
                    <th>当前持有人</th>
                    <th>时间</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="deviceTableBody">
                <tr>
                    <td colspan="7" class="loading">加载中...</td>
                </tr>
            </tbody>
        </table>

        <!-- 新增设备弹窗 -->
        <div id="addModal" class="modal">
            <div class="modal-content">
                <h3>新增设备</h3>
                <div class="form-group">
                    <label for="newDeviceModel">手机型号</label>
                    <input type="text" id="newDeviceModel" placeholder="例如：iPhone 15 Pro">
                </div>
                <div class="form-group">
                    <label for="newHolder">持有人</label>
                    <input type="text" id="newHolder" placeholder="请输入持有人姓名">
                </div>
                <div class="form-group">
                    <label for="newManager">负责人</label>
                    <input type="text" id="newManager" placeholder="请输入负责人姓名">
                </div>
                <div class="form-group">
                    <label for="newSystemVersion">系统版本</label>
                    <input type="text" id="newSystemVersion" placeholder="如：鸿蒙3.0、HarmonyOS NEXT 0.0.71">
                </div>
                <div class="form-group">
                    <label for="newRemark">手机备注</label>
                    <input type="text" id="newRemark" placeholder="如：AppleId账号、报废等">
                </div>
                <div class="form-group">
                    <label for="transferTime">流转时间</label>
                    <input type="datetime-local" id="transferTime" value="">
                </div>
                <div class="modal-buttons">
                    <button onclick="closeAddModal()">取消</button>
                    <button onclick="addDevice()">确定</button>
                </div>
            </div>
        </div>

        <!-- 流转历史弹窗 -->
        <div id="historyModal" class="modal">
            <div class="modal-content">
                <h3 id="historyTitle">流转历史</h3>
                <div class="history-table">
                    <table>
                        <thead>
                            <tr>
                                <th>持有人</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <tr>
                                <td colspan="2" class="loading">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-buttons">
                    <button onclick="closeHistoryModal()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API基础路径
        const API_BASE = '/device-management';

        // 页面加载时获取设备列表
        document.addEventListener('DOMContentLoaded', function() {
            loadDevices();
        });

        // 显示消息
        function showMessage(message, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        // 加载设备列表
        async function loadDevices() {
            const tbody = document.getElementById('deviceTableBody');
            
            try {
                // 显示加载状态
                tbody.innerHTML = '<tr><td colspan="7" class="loading">加载中...</td></tr>';
                
                // 调用列表API
                const response = await fetch(`${API_BASE}/list`);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '获取设备列表失败');
                }
                
                // 检查是否有数据
                if (!result.data || result.data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" class="empty-message">暂无设备记录</td></tr>';
                    return;
                }
                
                // 动态渲染表格数据
                tbody.innerHTML = result.data.map(device => `
                    <tr>
                        <td>${escapeHtml(device.device_model)}</td>
                        <td>${escapeHtml(device.system_version || '')}</td>
                        <td>${escapeHtml(device.manager || '')}</td>
                        <td>${escapeHtml(device.holder)}</td>
                        <td>${formatDateTime(device.transfer_time)}</td>
                        <td>${escapeHtml(device.remark || '')}</td>
                        <td>
                            <div class="action-cell">
                                <div class="holder-edit">
                                    <input type="text" 
                                           id="holder-${escapeHtml(device.device_model)}" 
                                           placeholder="更新持有人">
                                    <button class="small" 
                                            onclick="updateHolder('${escapeHtml(device.device_model)}', document.getElementById('holder-${escapeHtml(device.device_model)}').value)">
                                        确定
                                    </button>
                                </div>
                                <button class="small secondary" 
                                        onclick="showHistory('${escapeHtml(device.device_model)}')">
                                    查看流转
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('加载设备列表失败:', error);
                tbody.innerHTML = `<tr><td colspan="7" class="empty-message" style="color: #f44336;">加载失败: ${escapeHtml(error.message)}</td></tr>`;
                showMessage('加载设备列表失败: ' + error.message, 'error');
            }
        }
        
        // HTML转义函数，防止XSS攻击
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        // 显示新增设备弹窗
        function showAddModal() {
            document.getElementById('addModal').style.display = 'block';
            document.getElementById('newDeviceModel').value = '';
            document.getElementById('newHolder').value = '';
            document.getElementById('newManager').value = '';
            document.getElementById('newSystemVersion').value = '';
            document.getElementById('newRemark').value = '';
        }

        // 关闭新增设备弹窗
        function closeAddModal() {
            document.getElementById('addModal').style.display = 'none';
        }

        // 添加设备
        async function addDevice() {
            const deviceModel = document.getElementById('newDeviceModel').value.trim();
            const holder = document.getElementById('newHolder').value.trim();
            const manager = document.getElementById('newManager').value.trim();
            const systemVersion = document.getElementById('newSystemVersion').value.trim();
            const remark = document.getElementById('newRemark').value.trim();
            
            // 验证输入
            if (!deviceModel) {
                showMessage('请输入手机型号', 'error');
                return;
            }
            
            if (!holder) {
                showMessage('请输入持有人', 'error');
                return;
            }
            
            try {
                // 构建请求体
                const requestBody = {
                    device_model: deviceModel,
                    holder: holder
                };
                
                // 添加可选字段
                if (manager) requestBody.manager = manager;
                if (systemVersion) requestBody.system_version = systemVersion;
                if (remark) requestBody.remark = remark;
                
                // 调用添加API
                const response = await fetch(`${API_BASE}/add`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '添加设备失败');
                }
                
                // 添加成功
                showMessage(result.message || '添加设备成功', 'success');
                
                // 关闭弹窗
                closeAddModal();
                
                // 刷新设备列表
                await loadDevices();
                
            } catch (error) {
                console.error('添加设备失败:', error);
                showMessage('添加设备失败: ' + error.message, 'error');
            }
        }

        // 更新持有人
        async function updateHolder(deviceModel, newHolder) {
            // 验证输入
            if (!newHolder || !newHolder.trim()) {
                showMessage('请输入新持有人', 'error');
                return;
            }
            
            try {
                // 调用更新API
                const response = await fetch(`${API_BASE}/update`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_model: deviceModel,
                        holder: newHolder.trim()
                    })
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '更新持有人失败');
                }
                
                // 更新成功
                showMessage(result.message || '持有人更新成功', 'success');
                
                // 刷新设备列表
                await loadDevices();
                
            } catch (error) {
                console.error('更新持有人失败:', error);
                showMessage('更新持有人失败: ' + error.message, 'error');
            }
        }

        // 显示流转历史
        async function showHistory(deviceModel) {
            const historyModal = document.getElementById('historyModal');
            const historyTitle = document.getElementById('historyTitle');
            const historyTableBody = document.getElementById('historyTableBody');
            
            // 显示弹窗
            historyModal.style.display = 'block';
            
            // 设置标题
            historyTitle.textContent = `流转历史 - ${deviceModel}`;
            
            // 显示加载状态
            historyTableBody.innerHTML = '<tr><td colspan="2" class="loading">加载中...</td></tr>';
            
            try {
                // 调用历史API
                const response = await fetch(`${API_BASE}/history/${encodeURIComponent(deviceModel)}`);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '获取流转历史失败');
                }
                
                // 检查是否有数据
                if (!result.data || result.data.length === 0) {
                    historyTableBody.innerHTML = '<tr><td colspan="2" class="empty-message">暂无流转记录</td></tr>';
                    return;
                }
                
                // 动态渲染历史记录
                historyTableBody.innerHTML = result.data.map(record => `
                    <tr>
                        <td>${escapeHtml(record.holder)}</td>
                        <td>${formatDateTime(record.transfer_time)}</td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('获取流转历史失败:', error);
                historyTableBody.innerHTML = `<tr><td colspan="2" class="empty-message" style="color: #f44336;">加载失败: ${escapeHtml(error.message)}</td></tr>`;
                showMessage('获取流转历史失败: ' + error.message, 'error');
            }
        }

        // 关闭流转历史弹窗
        function closeHistoryModal() {
            document.getElementById('historyModal').style.display = 'none';
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const addModal = document.getElementById('addModal');
            const historyModal = document.getElementById('historyModal');
            
            if (event.target === addModal) {
                closeAddModal();
            }
            if (event.target === historyModal) {
                closeHistoryModal();
            }
        }
    </script>
</body>
</html>
