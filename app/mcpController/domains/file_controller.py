#!/usr/bin/env python3
"""
文件域控制器 - 电子签名相关功能
"""
import logging

from fastapi import APIRouter, Query

from app.core.mcp_decorator import mcp_endpoint
from mcpService.domains.file_service import (
    get_upload_url
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建文件域路由器
file_router = APIRouter(
    prefix="/file",  # 保持现有前缀
    tags=["文件域"],
    responses={404: {"description": "Not found"}}
)


@file_router.get(
    "/get_upload_url",
    summary="⚡ 获取文件id",
    description="上传文件前置操作，支持测试环境和模拟环境",
    operation_id="get_upload_url"
)
@mcp_endpoint
async def get_upload_url_endpoint(
        env: str = Query(..., description="环境类型，如'测试环境'"),  # 保持现有参数
        path: str = Query("./data/3页.pdf", description="文件路径"),
        convert2Pdf: bool = Query(False, description="是否转为pdf"),
        dedicatedCloud: bool = Query(False, description="专属云项目"),
        appId: str = Query(None, description="appId")
):
    """
    获取文件id

    参数说明:
        env: 环境类型，如"测试环境"，"模拟环境"
        path: 文件路径
        convert2Pdf: 是否转化为pfd，默认为否
        dedicatedCloud: 专属云项目id，默认为否
        fileName: 文件名，默认为空
        appId: appId，默认为空

    返回示例:
    {
      "code": 0,
      "message": "获取上传地址成功",
      "data": {
        "fileId": "fileId",
        "uploadUrl": "uploadUrl"
      }
    }
    """
    try:
        result = await get_upload_url(
            env=env,
            path=path,
            convert2Pdf=convert2Pdf,
            dedicatedCloud=dedicatedCloud,
            appId=appId
        )

        logger.info(f"获取上传地址完成: {result}")
        return result

    except Exception as e:
        logger.error(f"获取上传地址异常: {str(e)}")
        return {
            "status": "error",
            "message": f"获取上传地址异常: {str(e)}",
            "data": None
        }


# 导出router供main.py使用
router = file_router
