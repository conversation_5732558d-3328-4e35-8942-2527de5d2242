#!/usr/bin/env python3
"""
实名域控制器 - 实名认证相关功能
提供实名认证、身份验证等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.identity_service import (
    create_identity_verification
)
from app.core.mcp_decorator import mcp_endpoint

# 配置日志
logger = logging.getLogger(__name__)

# 创建实名域路由器
identity_router = APIRouter(
    prefix="/identity",
    tags=["实名域"],
    responses={404: {"description": "Not found"}}
)


@identity_router.post(
    "/create_verification",
    summary="🆔 创建实名认证",
    description="创建实名认证流程",
    operation_id="identity_create_verification"
)
@mcp_endpoint
async def create_identity_verification_endpoint(
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建实名认证"""
    return await create_identity_verification(environment)


# 导出router供main.py使用
router = identity_router