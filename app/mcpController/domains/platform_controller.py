#!/usr/bin/env python3
"""
平台控制器 - 造数平台通用功能
提供代码生成、提示词管理、XMind转换等核心功能
"""
import os
import logging
import base64
import hashlib
import httpx
from typing import Optional

from fastapi import APIRouter, Body

from mcpService.platform.prompt_service import (
    get_测试用例生成提示词, get_HttpRunner自动化用例生成提示词,
    get_简版测试用例生成提示词, get_XMind格式优化提示词
)
from mcpService.platform.code_generator import generate_mcp_code_from_curl
from mcpService.platform.conversion_service import ConversionService
from mcpService.platform.tms_platform_service import tms_platform_service
from app.core.mcp_decorator import mcp_endpoint

# 配置日志
logger = logging.getLogger(__name__)

# 创建平台路由器
platform_router = APIRouter(
    prefix="/platform",
    tags=["平台功能"],
    responses={404: {"description": "Not found"}}
)

# ============= 代码生成 =============

@platform_router.post(
    "/generate_mcp_code",
    summary="🧩 生成MCP代码",
    description="根据curl命令或API描述生成MCP代码",
    operation_id="platform_generate_mcp_code"
)
@mcp_endpoint
async def generate_mcp_code_endpoint(
    curl_command: str = Body(..., description="curl命令或API描述"),
    domain: Optional[str] = Body(None, description="业务域，如signing、saas等"),
    method_name: Optional[str] = Body(None, description="方法名称，不提供则自动生成")
):
    """根据curl命令或API描述生成MCP代码"""
    return await generate_mcp_code_from_curl(curl_command, domain, method_name)


@platform_router.post(
    "/get_testcase_generation_prompt",
    summary="🎯 获取测试用例生成提示词",
    description="获取资深软件测试专家AI提示词，用于生成完整的测试用例。当检测到关键字'测试用例'时，生成包含9个步骤的完整测试用例生成流程提示词，支持自动生成符合XMind导入标准的markdown文件",
    operation_id="platform_get_testcase_generation_prompt"
)
@mcp_endpoint
async def get_testcase_generation_prompt_endpoint():
    """获取测试用例生成提示词 - 资深软件测试专家AI提示词"""
    return await get_测试用例生成提示词()


@platform_router.post(
    "/get_httprunner_automation_prompt",
    summary="🚀 获取集测脚本生成提示词",
    description="获取资深软件测试专家AI提示词，用于生成HttpRunner自动化集测脚本。包含API信息收集、场景设计、用例生成、覆盖率检查等完整流程，自动生成可执行的YAML格式测试脚本",
    operation_id="platform_get_httprunner_automation_prompt"
)
@mcp_endpoint
async def get_httprunner_automation_prompt_endpoint():
    """获取HttpRunner自动化用例生成提示词 - 专业自动化测试AI提示词"""
    return await get_HttpRunner自动化用例生成提示词()


# @platform_router.post(
#     "/get_simple_testcase_prompt",
#     summary="📝 获取简版测试用例生成提示词",
#     description="获取简化版测试用例生成AI提示词，包含需求分析、场景设计、用例生成等核心流程，快速生成标准格式的测试用例",
#     operation_id="platform_get_simple_testcase_prompt"
# )
# @mcp_endpoint
# async def get_simple_testcase_prompt_endpoint():
#     """获取简版测试用例生成提示词 - 快速测试用例生成AI提示词"""
#     return await get_简版测试用例生成提示词()


# ============= 提示词管理 =============

# @platform_router.post(
#     "/get_prompt_types",
#     summary="📋 获取提示词类型",
#     description="获取所有可用的提示词类型",
#     operation_id="platform_get_prompt_types"
# )
# async def get_prompt_types_endpoint():
#     """获取所有可用的提示词类型"""
#     return get_prompt_types()
#
#
# @platform_router.post(
#     "/get_prompt_content",
#     summary="📄 获取提示词内容",
#     description="获取指定类型的提示词内容",
#     operation_id="platform_get_prompt_content"
# )
# async def get_prompt_content_endpoint(prompt_type: str):
#     """获取指定类型的提示词内容"""
#     return get_prompt_content(prompt_type)
#
#
#
#
#
# @platform_router.post(
#     "/get_interface_generation_prompt",
#     summary="🔧 获取接口生成提示词",
#     description="获取接口生成提示词",
#     operation_id="platform_get_interface_generation_prompt"
# )
# async def get_interface_generation_prompt_endpoint():
#     """获取接口生成提示词"""
#     return get_接口生成提示词()
#
#
# @platform_router.post(
#     "/get_required_params_prompt",
#     summary="📝 获取必填参数提示词",
#     description="获取必填参数提示词",
#     operation_id="platform_get_required_params_prompt"
# )
# async def get_required_params_prompt_endpoint():
#     """获取必填参数提示词"""
#     return get_必填参数提示词()
#
#
# @platform_router.post(
#     "/get_enum_values_prompt",
#     summary="📊 获取枚举值提示词",
#     description="获取枚举值提示词",
#     operation_id="platform_get_enum_values_prompt"
# )
# async def get_enum_values_prompt_endpoint():
#     """获取枚举值提示词"""
#     return get_枚举值提示词()
#
#
# @platform_router.post(
#     "/get_required_enum_merged_prompt",
#     summary="📋 获取必填与枚举合并提示词",
#     description="获取必填与枚举合并提示词",
#     operation_id="platform_get_required_enum_merged_prompt"
# )
# async def get_required_enum_merged_prompt_endpoint():
#     """获取必填与枚举合并提示词"""
#     return get_必填与枚举合并提示词()
#
#

# ============= TMS平台集成服务（精简版） =============

@platform_router.post(
    "/get_version_info",
    summary="🎯 获取版本信息",
    description="通过版本名称获取版本信息",
    operation_id="platform_get_version_info"
)
@mcp_endpoint
async def get_version_info_endpoint(
    version_name: str = Body(..., description="版本名称"),
    creator_alias: Optional[str] = Body(None, description="创建者花名（可选）")
):
    """获取版本信息"""
    return await tms_platform_service.get_version_info_by_version_name(version_name, creator_alias, None)


@platform_router.post(
    "/get_upload_info",
    summary="📤 获取上传信息",
    description="获取上传所需的参数信息",
    operation_id="platform_get_upload_info"
)
@mcp_endpoint
async def get_upload_info_endpoint(
    version_name: str = Body(..., description="版本名称"),
    uploader_alias: str = Body(..., description="上传者花名")
):
    """获取上传信息"""
    return await tms_platform_service.get_upload_params(version_name, None, None, uploader_alias, None)


# ============= 核心XMind功能 =============

@platform_router.post(
    "/convert_to_xmind",
    summary="📋 转换为XMind",
    description="将Markdown文件转换为XMind格式",
    operation_id="platform_convert_to_xmind"
)
@mcp_endpoint
async def convert_to_xmind_endpoint(
    markdown_path: str = Body(..., description="Markdown文件路径"),
    output_path: Optional[str] = Body(None, description="输出路径（可选）"),
    title: Optional[str] = Body(None, description="标题（可选）")
):
    """转换为XMind格式"""
    return await ConversionService.convert_markdown_to_xmind(markdown_path, output_path, title)


@platform_router.post(
    "/upload_xmind_file",
    summary="📤 上传XMind文件",
    description="上传XMind文件到平台",
    operation_id="platform_upload_xmind_file"
)
@mcp_endpoint
async def upload_xmind_file_endpoint(
    file_path: str = Body(..., description="XMind文件路径"),
    version_name: str = Body(..., description="版本名称"),
    uploader_alias: str = Body(..., description="上传者花名")
):
    """上传XMind文件"""
    from mcpService.platform.upload_service import UploadService
    
    upload_params = await tms_platform_service.get_upload_params(version_name, None, None, uploader_alias, None)
    if upload_params:
        version_id = upload_params.get("versionId")
        user_id = upload_params.get("userId")
        if version_id and user_id:
            return await UploadService.upload_xmind(file_path, user_id, version_id)
    
    return {"success": False, "message": "无法获取上传参数"}


@platform_router.post(
    "/convert_markdown_content",
    summary="📝 转换Markdown内容",
    description="上传到用例平台",
    operation_id="platform_convert_markdown_content"
)
@mcp_endpoint
async def convert_markdown_content_endpoint(
    content: str = Body(..., description="Markdown文件内容"),
    filename: str = Body(..., description="原始文件名"),
    title: Optional[str] = Body(None, description="XMind标题（可选）"),
    version_name: Optional[str] = Body(None, description="版本名称（可选，用于自动上传）"),
    uploader_alias: Optional[str] = Body(None, description="上传者花名（可选，用于自动上传）")
):
    """直接转换Markdown内容为XMind格式并上传到用例平台"""
    try:
        # 创建临时目录
        temp_dir = os.path.join(os.getcwd(), "data", "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 保存Markdown内容到临时文件
        temp_file_path = os.path.join(temp_dir, filename)
        with open(temp_file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        # 转换为XMind
        conversion_result = await ConversionService.convert_markdown_to_xmind(
            markdown_path=temp_file_path,
            title=title or os.path.splitext(filename)[0]
        )
        
        if conversion_result.get("status") != "success":
            return {
                "success": False,
                "message": f"转换失败: {conversion_result.get('message', '未知错误')}",
                "data": None
            }
        
        xmind_file_path = conversion_result.get("file_path")
        
        # 如果提供了版本信息，自动上传到TMS平台
        upload_result = None
        if version_name and uploader_alias:
            upload_params = await tms_platform_service.get_upload_params(version_name, None, None, uploader_alias, None)
            if upload_params:
                version_id = upload_params.get("versionId")
                user_id = upload_params.get("userId")
                if version_id and user_id:
                    from mcpService.platform.upload_service import UploadService
                    upload_result = await UploadService.upload_xmind(xmind_file_path, user_id, version_id)
        
        # 清理临时文件
        try:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        except:
            pass
        
        return {
            "success": True,
            "message": "Markdown内容转换成功",
            "data": {
                "original_file": {
                    "filename": filename,
                    "size": len(content.encode('utf-8'))
                },
                "converted_file": {
                    "path": xmind_file_path,
                    "title": title or os.path.splitext(filename)[0]
                },
                "upload_result": upload_result
            }
        }
    
    except Exception as e:
        logger.error(f"Markdown内容转换失败: {str(e)}")
        return {
            "success": False,
            "message": f"Markdown内容转换失败: {str(e)}",
            "data": None
        }


# ============= 通用文件上传服务 =============

# 固定的上传文件目录
UPLOAD_FILES_DIR = os.path.join(os.getcwd(), "data", "upload_files")

def ensure_upload_dir():
    """确保上传目录存在"""
    os.makedirs(UPLOAD_FILES_DIR, exist_ok=True)

def get_file_base64_md5(file_path: str) -> str:
    """获取文件的Base64编码MD5值"""
    try:
        with open(file_path, 'rb') as fd:
            m = hashlib.md5()
            while True:
                d = fd.read(8096)
                if not d:
                    break
                m.update(d)
            byte = base64.b64encode(m.digest())
            return bytes.decode(byte)
    except Exception as e:
        logger.error(f"计算文件MD5失败: {str(e)}")
        raise


def get_file_size(file_path: str) -> int:
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.error(f"获取文件大小失败: {str(e)}")
        raise


@platform_router.post(
    "/upload_file_to_tsign",
    summary="📤 上传文件到公有云",
    description="从固定目录上传文件到公有云，支持各种文件类型",
    operation_id="platform_upload_file_to_tsign"
)
@mcp_endpoint
async def upload_file_to_tsign_endpoint(
    file_name: str = Body(..., description="文件名（从data/upload_files目录读取）"),
    app_id: str = Body(..., description="应用ID"),
    env: str = Body("Test", description="环境：Test或sml"),
    convert_to_pdf: bool = Body(False, description="是否转换为PDF"),
    resolve_form: bool = Body(False, description="是否解析表单")
):
    """上传文件到公有云"""
    try:
        # 确保上传目录存在
        ensure_upload_dir()
        
        # 构建完整文件路径
        file_path = os.path.join(UPLOAD_FILES_DIR, file_name)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return {
                "success": False,
                "message": f"文件不存在: {file_path}",
                "data": {
                    "upload_dir": UPLOAD_FILES_DIR,
                    "requested_file": file_name
                }
            }
        
        # 获取文件信息
        file_md5 = get_file_base64_md5(file_path)
        file_size = get_file_size(file_path)
        
        logger.info(f"上传文件: {file_name}, MD5: {file_md5}, 大小: {file_size}")
        
        # 构建API URL
        base_url = "http://in-test-openapi.tsign.cn" if env == "Test" else "http://in-sml-openapi.tsign.cn"
        url = f"{base_url}/v1/files/getUploadUrl"
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-App-Id": app_id,
            "X-Tsign-Open-Auth-Mode": "simple",
            "x-tsign-client-id": "pass-test"
        }
        
        # 构建请求体
        body = {
            "contentMd5": file_md5,
            "contentType": "application/octet-stream",
            "fileName": file_name,
            "fileSize": file_size,
            "convert2Pdf": convert_to_pdf,
            "resolveForm": resolve_form
        }
        
        logger.info(f"请求URL: {url}")
        logger.info(f"请求体: {body}")
        
        # 获取上传URL（使用异步 httpx）
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=body, headers=headers)
        logger.info(f"获取上传URL响应: {response.status_code}, {response.text}")
        
        if response.status_code != 200:
            return {
                "success": False,
                "message": f"获取上传URL失败: {response.status_code} - {response.text}",
                "data": None
            }
        
        result = response.json()
        # 检查API返回的code字段，0表示成功
        if result.get('code', -1) != 0:
            return {
                "success": False,
                "message": f"获取上传URL失败: {result.get('message', '未知错误')}",
                "data": result
            }
        
        upload_url = result['data']['uploadUrl']
        file_id = result['data']['fileId']
        
        # 上传文件
        upload_headers = {
            "Content-MD5": file_md5,
            "Content-Type": "application/octet-stream"
        }
        
        logger.info(f"上传头信息: {upload_headers}")
        
        # 上传文件（使用异步 httpx）
        with open(file_path, 'rb') as file_data:
            file_content = file_data.read()
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            upload_response = await client.put(upload_url, content=file_content, headers=upload_headers)
        
        logger.info(f"文件上传响应: {upload_response.status_code}")
        
        if upload_response.status_code == 200:
            return {
                "success": True,
                "message": f"文件上传成功，fileId: {file_id}",
                "fileId": file_id,
                "data": {
                    "fileId": file_id,
                    "fileName": file_name,
                    "fileSize": file_size,
                    "fileMd5": file_md5,
                    "uploadStatus": "success"
                }
            }
        else:
            return {
                "success": False,
                "message": f"文件上传失败: {upload_response.status_code} - {upload_response.text}",
                "fileId": file_id,
                "data": {
                    "fileId": file_id,
                    "uploadStatus": upload_response.status_code,
                    "uploadResponse": upload_response.text
                }
            }
    
    except Exception as e:
        logger.error(f"文件上传异常: {str(e)}")
        return {
            "success": False,
            "message": f"文件上传异常: {str(e)}",
            "data": None
        }


@platform_router.post(
    "/list_upload_files",
    summary="📋 列出上传目录文件",
    description="列出data/upload_files目录中的所有文件",
    operation_id="platform_list_upload_files"
)
@mcp_endpoint
async def list_upload_files_endpoint():
    """列出上传目录中的所有文件"""
    try:
        # 确保上传目录存在
        ensure_upload_dir()
        
        files = []
        if os.path.exists(UPLOAD_FILES_DIR):
            for file_name in os.listdir(UPLOAD_FILES_DIR):
                file_path = os.path.join(UPLOAD_FILES_DIR, file_name)
                if os.path.isfile(file_path):
                    file_size = get_file_size(file_path)
                    files.append({
                        "name": file_name,
                        "size": file_size,
                        "path": file_path
                    })
        
        return {
            "success": True,
            "message": f"找到 {len(files)} 个文件",
            "data": {
                "upload_dir": UPLOAD_FILES_DIR,
                "files": files,
                "total_count": len(files)
            }
        }
    
    except Exception as e:
        logger.error(f"列出文件失败: {str(e)}")
        return {
            "success": False,
            "message": f"列出文件失败: {str(e)}",
            "data": None
        }

router = platform_router