#!/usr/bin/env python3
"""
创建个人和企业测试账号并获取签署链接的脚本
"""

import asyncio
import json
from mcpService.domains.saas_service import register_test_person_account, register_test_company_account
from mcpService.domains.signing_service import get_sign_url
from mcpService.common.OpenApiFactory import OpenApiFactory


async def main():
    # 初始化环境
    env = "测试环境"
    data_provider = OpenApiFactory.create(env)
    
    print("开始创建个人和企业测试账号...")
    
    # 创建个人测试账号
    person_data = {
        "app_id": data_provider.default_app_id,
        "idNo": "110101199003073578",
        "mobile": "***********",
        "name": "张三",
        "thirdPartyUserId": "person_test_001",
        "environment": env
    }
    
    print(f"正在创建个人测试账号: {person_data['name']}")
    person_result = await register_test_person_account(**person_data)
    
    if person_result.get("status") == "success":
        print("✓ 个人测试账号创建成功")
        person_account_id = person_result.get("data", {}).get("accountId", "")
        print(f"  个人账号ID: {person_account_id}")
    else:
        print("✗ 个人测试账号创建失败")
        print(f"  错误信息: {person_result.get('message', '未知错误')}")
        return
    
    # 创建企业测试账号
    company_data = {
        "app_id": data_provider.default_app_id,
        "idNumber": "110101199003073579",
        "mobile": "***********",
        "name": "测试企业",
        "thirdPartyUserId": "company_test_001",
        "orgLegalIdNumber": "110101199003073579",
        "orgLegalName": "李四",
        "environment": env
    }
    
    print(f"正在创建企业测试账号: {company_data['name']}")
    company_result = await register_test_company_account(**company_data)
    
    if company_result.get("status") == "success":
        print("✓ 企业测试账号创建成功")
        company_account_id = company_result.get("data", {}).get("accountId", "")
        print(f"  企业账号ID: {company_account_id}")
    else:
        print("✗ 企业测试账号创建失败")
        print(f"  错误信息: {company_result.get('message', '未知错误')}")
        return
    
    # 使用创建的账号获取签署链接
    print("\n开始生成签署链接...")
    
    # 个人签署链接
    print("正在生成个人签署链接...")
    person_signer_list = [
        {
            "签署人类型": "个人",
            "手机号": person_data["mobile"]
        }
    ]
    
    person_sign_result = await get_sign_url(
        env=env,
        signer_list=person_signer_list,
        发起方="个人"
    )
    
    if person_sign_result.get("status") == "success":
        print("✓ 个人签署链接生成成功")
        person_sign_url = person_sign_result.get("data", {}).get("shortUrl", "")
        print(f"  个人签署链接: {person_sign_url}")
    else:
        print("✗ 个人签署链接生成失败")
        print(f"  错误信息: {person_sign_result.get('message', '未知错误')}")
    
    # 企业签署链接
    print("正在生成企业签署链接...")
    company_signer_list = [
        {
            "签署人类型": "企业",
            "企业名称": company_data["name"],
            "手机号": company_data["mobile"]
        }
    ]
    
    company_sign_result = await get_sign_url(
        env=env,
        signer_list=company_signer_list,
        发起方="企业"
    )
    
    if company_sign_result.get("status") == "success":
        print("✓ 企业签署链接生成成功")
        company_sign_url = company_sign_result.get("data", {}).get("shortUrl", "")
        print(f"  企业签署链接: {company_sign_url}")
    else:
        print("✗ 企业签署链接生成失败")
        print(f"  错误信息: {company_sign_result.get('message', '未知错误')}")
    
    # 混合签署链接（个人和企业）
    print("正在生成混合签署链接...")
    mixed_signer_list = [
        {
            "签署人类型": "个人",
            "手机号": person_data["mobile"],
            "顺序": 1
        },
        {
            "签署人类型": "企业",
            "企业名称": company_data["name"],
            "手机号": company_data["mobile"],
            "顺序": 2
        }
    ]
    
    mixed_sign_result = await get_sign_url(
        env=env,
        signer_list=mixed_signer_list,
        发起方="平台"
    )
    
    if mixed_sign_result.get("status") == "success":
        print("✓ 混合签署链接生成成功")
        mixed_sign_url = mixed_sign_result.get("data", {}).get("shortUrl", "")
        print(f"  混合签署链接: {mixed_sign_url}")
    else:
        print("✗ 混合签署链接生成失败")
        print(f"  错误信息: {mixed_sign_result.get('message', '未知错误')}")
    
    # 输出总结
    print("\n" + "="*50)
    print("执行总结:")
    print("="*50)
    print(f"个人账号创建: {'成功' if person_result.get('status') == 'success' else '失败'}")
    print(f"企业账号创建: {'成功' if company_result.get('status') == 'success' else '失败'}")
    print(f"个人签署链接生成: {'成功' if person_sign_result.get('status') == 'success' else '失败'}")
    print(f"企业签署链接生成: {'成功' if company_sign_result.get('status') == 'success' else '失败'}")
    print(f"混合签署链接生成: {'成功' if mixed_sign_result.get('status') == 'success' else '失败'}")


if __name__ == "__main__":
    asyncio.run(main())