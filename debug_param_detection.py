#!/usr/bin/env python3
"""
调试参数检测
"""
import inspect
from main import create_app

def debug_parameter_detection():
    """调试参数检测"""
    print("🔍 调试参数检测...")
    
    # 创建应用
    app = create_app()
    
    # 查找具体的路由
    for route in app.routes:
        if hasattr(route, 'path') and route.path == "/certificate/create":
            print(f"找到路由: {route.path}")
            print(f"方法: {route.methods}")
            
            if hasattr(route, 'endpoint') and route.endpoint:
                func = route.endpoint
                print(f"函数: {func.__name__}")
                
                # 检查函数签名
                sig = inspect.signature(func)
                for param_name, param in sig.parameters.items():
                    if param_name in ['self', 'cls', 'request', 'response']:
                        continue
                    
                    print(f"\n参数: {param_name}")
                    print(f"  类型注解: {param.annotation}")
                    print(f"  默认值: {param.default}")
                    print(f"  默认值类型: {type(param.default)}")
                    print(f"  默认值类名: {param.default.__class__.__name__ if hasattr(param.default, '__class__') else 'None'}")
                    print(f"  是否为空: {param.default == inspect.Parameter.empty}")
                    
                    # 检查是否有FastAPI属性
                    if hasattr(param.default, '__class__'):
                        print(f"  有__class__属性")
                        if hasattr(param.default, 'default'):
                            print(f"  有default属性: {param.default.default}")
                        if hasattr(param.default, 'description'):
                            print(f"  有description属性: {param.default.description}")
                    else:
                        print(f"  没有__class__属性")
            break

if __name__ == "__main__":
    debug_parameter_detection()