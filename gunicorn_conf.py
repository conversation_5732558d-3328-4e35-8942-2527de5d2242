#!/usr/bin/env python3
"""
Gunicorn 配置文件
用于生产环境启动多个 Uvicorn worker
"""
import os
import multiprocessing

# 服务器配置
bind = f"{os.getenv('HOST', '0.0.0.0')}:{os.getenv('PORT', '8000')}"
backlog = int(os.getenv("BACKLOG", "2048"))

# Worker 配置
# Worker 数量：默认为 (2 * CPU核心数) + 1，可通过环境变量覆盖
# 限制最小 1 个，最大 16 个 worker，避免在 Docker 环境中创建过多 worker
cpu_count = multiprocessing.cpu_count()
default_workers = min(max((2 * cpu_count) + 1, 1), 16)
workers = int(os.getenv("WORKERS", default_workers))
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = int(os.getenv("WORKER_CONNECTIONS", "1000"))

# 超时配置
timeout = int(os.getenv("WORKER_TIMEOUT", "120"))  # Worker 超时时间（秒）
graceful_timeout = int(os.getenv("GRACEFUL_TIMEOUT", "300"))  # 优雅关闭超时时间（秒）
keepalive = int(os.getenv("KEEP_ALIVE_TIMEOUT", "604800"))  # Keep-alive 超时（7天）

# 进程名称
proc_name = "esign-qa-mcp-platform"

# 日志配置
accesslog = os.getenv("ACCESS_LOG", "-")  # 访问日志，"-" 表示输出到 stdout
errorlog = os.getenv("ERROR_LOG", "-")  # 错误日志，"-" 表示输出到 stderr
loglevel = os.getenv("LOG_LEVEL", "info").lower()
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
daemon = False  # 不以后台进程运行（Docker 容器中需要前台运行）
pidfile = None  # PID 文件路径（可选）
umask = 0  # 文件权限掩码
user = None  # 运行用户（可选）
group = None  # 运行组（可选）
tmp_upload_dir = None  # 临时上传目录

# 性能优化
preload_app = False  # 不预加载应用（多进程环境下可能导致问题）
# max_requests = int(os.getenv("MAX_REQUESTS", "10000"))  # 单个 worker 最大请求数
max_requests_jitter = int(os.getenv("MAX_REQUESTS_JITTER", "1000"))  # 请求数抖动，避免同时重启

# 线程配置（如果需要）
threads = int(os.getenv("THREADS", "1"))  # 每个 worker 的线程数

# SSL 配置（如果需要 HTTPS）
# keyfile = None
# certfile = None

def on_starting(server):
    """服务器启动时调用"""
    server.log.info("🚀 Gunicorn 服务器启动中...")

def on_reload(server):
    """重载时调用"""
    server.log.info("🔄 Gunicorn 服务器重载中...")

def when_ready(server):
    """服务器就绪时调用"""
    server.log.info(f"✅ Gunicorn 服务器就绪，{workers} 个 worker 已启动")
    server.log.info(f"📚 API文档: http://{bind}/docs")
    server.log.info(f"🔌 MCP端点: http://{bind}/mcp")

def worker_int(worker):
    """Worker 收到 INT 信号时调用"""
    worker.log.info("⚠️ Worker 收到中断信号")

def pre_fork(server, worker):
    """Fork worker 之前调用"""
    pass

def post_fork(server, worker):
    """Fork worker 之后调用"""
    server.log.info(f"✅ Worker {worker.pid} 已启动")

def post_worker_init(worker):
    """Worker 初始化后调用"""
    pass

def worker_abort(worker):
    """Worker 异常退出时调用"""
    import traceback
    import sys
    worker.log.error(f"⚠️ Worker {worker.pid} 异常退出")
    try:
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_type:
            worker.log.error(f"异常类型: {exc_type.__name__}")
            worker.log.error(f"异常信息: {exc_value}")
            worker.log.error(f"异常堆栈:\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}")
    except Exception as e:
        worker.log.error(f"获取异常信息失败: {e}")

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("👋 Gunicorn 服务器已关闭")

