2025-09-29 15:14:27,552 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-29 15:14:27,553 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-29 15:14:28,337 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-29 15:14:28,511 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-29 15:14:28,511 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '实名域', '意愿域', '签署域', 'SaaS域', '文件域', '平台功能', 'wiki域', '费用域']
2025-09-29 15:14:28,511 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-29 15:14:28,511 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-29 15:14:28,523 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-29 15:14:28,529 - __main__ - INFO - certificate路由加载成功
2025-09-29 15:14:28,531 - __main__ - INFO - fee路由加载成功
2025-09-29 15:14:28,532 - __main__ - INFO - file路由加载成功
2025-09-29 15:14:28,534 - __main__ - INFO - identity路由加载成功
2025-09-29 15:14:28,539 - __main__ - INFO - intention路由加载成功
2025-09-29 15:14:28,551 - __main__ - INFO - platform路由加载成功
2025-09-29 15:14:28,558 - __main__ - INFO - saas路由加载成功
2025-09-29 15:14:28,569 - __main__ - INFO - signing路由加载成功
2025-09-29 15:14:28,570 - __main__ - INFO - wiki路由加载成功
2025-09-29 15:14:28,572 - __main__ - INFO - 🚀 设置MCP服务...
2025-09-29 15:14:28,572 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '实名域', '意愿域', '签署域', 'SaaS域', '文件域', '平台功能', 'wiki域', '费用域']
2025-09-29 15:14:28,610 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-29 15:14:28,610 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-29 15:14:28,610 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-29 15:14:28,610 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-29 15:14:28,610 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-09-29 15:14:28,610 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-09-29 15:14:28,610 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-29 15:14:28,610 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-09-29 15:14:28,610 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-09-29 15:14:30,268 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-29 15:14:30,269 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-29 15:14:31,865 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-29 15:14:32,043 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-29 15:14:32,044 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', 'wiki域', 'SaaS域', '实名域', '费用域', '文件域', '意愿域', '签署域', '证书域']
2025-09-29 15:14:32,044 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-29 15:14:32,044 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-29 15:14:32,044 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-29 15:14:32,049 - __mp_main__ - INFO - certificate路由加载成功
2025-09-29 15:14:32,051 - __mp_main__ - INFO - fee路由加载成功
2025-09-29 15:14:32,052 - __mp_main__ - INFO - file路由加载成功
2025-09-29 15:14:32,054 - __mp_main__ - INFO - identity路由加载成功
2025-09-29 15:14:32,058 - __mp_main__ - INFO - intention路由加载成功
2025-09-29 15:14:32,069 - __mp_main__ - INFO - platform路由加载成功
2025-09-29 15:14:32,077 - __mp_main__ - INFO - saas路由加载成功
2025-09-29 15:14:32,088 - __mp_main__ - INFO - signing路由加载成功
2025-09-29 15:14:32,090 - __mp_main__ - INFO - wiki路由加载成功
2025-09-29 15:14:32,092 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-29 15:14:32,092 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', 'wiki域', 'SaaS域', '实名域', '费用域', '文件域', '意愿域', '签署域', '证书域']
2025-09-29 15:14:32,127 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-29 15:14:32,127 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-29 15:14:32,127 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-29 15:14:32,127 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-29 15:14:32,192 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-29 15:14:32,193 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-29 15:14:32,193 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', 'wiki域', 'SaaS域', '实名域', '费用域', '文件域', '意愿域', '签署域', '证书域']
2025-09-29 15:14:32,194 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-29 15:14:32,194 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-29 15:14:32,194 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-29 15:14:32,199 - main - INFO - certificate路由加载成功
2025-09-29 15:14:32,202 - main - INFO - fee路由加载成功
2025-09-29 15:14:32,203 - main - INFO - file路由加载成功
2025-09-29 15:14:32,205 - main - INFO - identity路由加载成功
2025-09-29 15:14:32,210 - main - INFO - intention路由加载成功
2025-09-29 15:14:32,222 - main - INFO - platform路由加载成功
2025-09-29 15:14:32,231 - main - INFO - saas路由加载成功
2025-09-29 15:14:32,240 - main - INFO - signing路由加载成功
2025-09-29 15:14:32,241 - main - INFO - wiki路由加载成功
2025-09-29 15:14:32,242 - main - INFO - 🚀 设置MCP服务...
2025-09-29 15:14:32,242 - main - INFO - 🏷️ 使用自动发现的标签: ['平台功能', 'wiki域', 'SaaS域', '实名域', '费用域', '文件域', '意愿域', '签署域', '证书域']
2025-09-29 15:14:32,325 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-29 15:14:32,325 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-29 15:14:32,325 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-29 15:14:32,325 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-29 15:14:32,326 - main - INFO - 🚀 应用启动中...
2025-09-29 15:14:32,326 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-29 15:14:32,326 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-29 15:14:32,327 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-29 15:14:32,327 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-29 15:14:32,327 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-29 15:14:32,327 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-29 15:14:42,290 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'签署人类型': '经办人', '签署区样式': '单页', '签署区类型': '普通', '顺序': 2}], group=default, FDA=True
2025-09-29 15:14:42,705 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "984bcad0666c4d329658586f86e1e9f0"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b", "signConfig": {"signType": "FDAType"}}, "signers": [{"orgSignerInfo": {"orgName": "esigntest\u674e\u4fdd\u8f89\u80fd\u529b\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 382.**************, "positionY": 473.**************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 3}]}
2025-09-29 15:14:42,705 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 1435002, "message": "\u53c2\u6570\u9519\u8bef: \u7ecf\u529e\u4eba\u7b7e\u7684\u7b7e\u7f72\u533a\u9700\u5b58\u5728\u4e00\u4e2a\u76f8\u540c\u7b7e\u7f72\u987a\u5e8f\u7684\u673a\u6784\u7b7e\u7b7e\u7f72\u533a\uff0c\u5373signOrder\u76f8\u540c\u4e14signerType=1", "data": null}
2025-09-29 15:14:42,705 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'error', 'message': '创建签署流程失败！', 'timestamp': '2025-09-29T15:14:42.705367', 'details': {'code': 1435002, 'message': '参数错误: 经办人签的签署区需存在一个相同签署顺序的机构签签署区，即signOrder相同且signerType=1', 'data': None}}
2025-09-29 16:17:17,794 - main - INFO - 🛑 应用关闭中...
2025-09-29 16:17:17,796 - main - INFO - ✅ 连接状态已保存
2025-09-29 16:17:17,796 - main - INFO - ✅ 应用已关闭
