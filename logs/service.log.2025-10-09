2025-10-09 14:51:55,170 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:51:55,173 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:51:56,545 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-09 14:51:56,888 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-09 14:51:56,888 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '签署域', '意愿域', '费用域', '文件域', '证书域', '平台功能', 'SaaS域', '实名域']
2025-10-09 14:51:56,889 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:51:56,889 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:51:56,889 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:51:56,898 - __main__ - INFO - certificate路由加载成功
2025-10-09 14:51:56,902 - __main__ - INFO - fee路由加载成功
2025-10-09 14:51:56,905 - __main__ - INFO - file路由加载成功
2025-10-09 14:51:56,908 - __main__ - INFO - identity路由加载成功
2025-10-09 14:51:56,915 - __main__ - INFO - intention路由加载成功
2025-10-09 14:51:56,935 - __main__ - INFO - platform路由加载成功
2025-10-09 14:51:56,951 - __main__ - INFO - saas路由加载成功
2025-10-09 14:51:56,973 - __main__ - INFO - signing路由加载成功
2025-10-09 14:51:56,976 - __main__ - INFO - wiki路由加载成功
2025-10-09 14:51:56,981 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-09 14:51:56,981 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '签署域', '意愿域', '费用域', '文件域', '证书域', '平台功能', 'SaaS域', '实名域']
2025-10-09 14:51:57,042 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:51:57,042 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:51:57,043 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:51:57,043 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:51:57,043 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-09 14:51:57,043 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-09 14:51:57,043 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:51:57,043 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-09 14:51:57,043 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-09 14:51:59,410 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:51:59,411 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:52:00,474 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-09 14:52:00,778 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-09 14:52:00,779 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', '证书域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '实名域', '文件域', '费用域']
2025-10-09 14:52:00,779 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:52:00,779 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:52:00,779 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:52:00,789 - __mp_main__ - INFO - certificate路由加载成功
2025-10-09 14:52:00,791 - __mp_main__ - INFO - fee路由加载成功
2025-10-09 14:52:00,793 - __mp_main__ - INFO - file路由加载成功
2025-10-09 14:52:00,796 - __mp_main__ - INFO - identity路由加载成功
2025-10-09 14:52:00,803 - __mp_main__ - INFO - intention路由加载成功
2025-10-09 14:52:00,819 - __mp_main__ - INFO - platform路由加载成功
2025-10-09 14:52:00,838 - __mp_main__ - INFO - saas路由加载成功
2025-10-09 14:52:00,852 - __mp_main__ - INFO - signing路由加载成功
2025-10-09 14:52:00,855 - __mp_main__ - INFO - wiki路由加载成功
2025-10-09 14:52:00,858 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-09 14:52:00,859 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['签署域', '证书域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '实名域', '文件域', '费用域']
2025-10-09 14:52:00,911 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:52:00,911 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:52:00,911 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:52:00,911 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:52:01,044 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:52:01,045 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:52:01,046 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', '证书域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '实名域', '文件域', '费用域']
2025-10-09 14:52:01,048 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:52:01,048 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:52:01,048 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:52:01,061 - main - INFO - certificate路由加载成功
2025-10-09 14:52:01,065 - main - INFO - fee路由加载成功
2025-10-09 14:52:01,067 - main - INFO - file路由加载成功
2025-10-09 14:52:01,070 - main - INFO - identity路由加载成功
2025-10-09 14:52:01,079 - main - INFO - intention路由加载成功
2025-10-09 14:52:01,099 - main - INFO - platform路由加载成功
2025-10-09 14:52:01,115 - main - INFO - saas路由加载成功
2025-10-09 14:52:01,133 - main - INFO - signing路由加载成功
2025-10-09 14:52:01,136 - main - INFO - wiki路由加载成功
2025-10-09 14:52:01,138 - main - INFO - 🚀 设置MCP服务...
2025-10-09 14:52:01,138 - main - INFO - 🏷️ 使用自动发现的标签: ['签署域', '证书域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '实名域', '文件域', '费用域']
2025-10-09 14:52:01,235 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:52:01,235 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:52:01,235 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:52:01,235 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:52:01,236 - main - INFO - 🚀 应用启动中...
2025-10-09 14:52:01,236 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-09 14:52:01,237 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-09 14:52:01,237 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-09 14:52:01,237 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-09 14:52:01,237 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-09 14:52:01,237 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-09 14:52:05,799 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:52:05,799 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:52:05,799 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:52:11,462 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:52:11,464 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:52:11,464 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "9100000039THRHPKXH", "idNo": "350700196103291927", "name": "测试晁茗黛", "englishName": "Sarah Susan Kelley", "bankCard": "****************", "phone": "***********", "orgName": "esigntest晁茗黛经营的个体工商户"}]}
2025-10-09 14:52:11,544 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:52:11,544 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:52:11,544 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:52:14,721 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:52:14,722 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:52:14,722 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:52:14,761 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:52:14,761 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:52:14,761 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:52:16,660 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:52:16,660 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:52:16,660 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:52:16,688 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:52:16,688 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:52:16,689 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:52:18,761 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:52:18,762 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:52:18,762 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:53:17,140 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:53:17,140 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:53:17,140 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:53:19,050 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:53:19,050 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:53:19,051 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "910000001PB6QFE738", "idNo": "51140019610703042X", "name": "测试索姬", "englishName": "Edna Jeanne Jenkins", "bankCard": "****************", "phone": "***********", "orgName": "esigntest索姬经营的个体工商户"}]}
2025-10-09 14:53:19,132 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:53:19,133 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:53:19,133 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:53:21,025 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:53:21,026 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:53:21,026 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:53:21,059 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:53:21,060 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:53:21,060 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:53:23,168 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:53:23,168 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:53:23,168 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:53:23,195 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:53:23,195 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:53:23,195 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:53:25,200 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:53:25,201 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:53:25,201 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:54:08,607 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:54:08,607 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:54:08,607 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:54:11,513 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:54:11,514 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:54:11,514 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000WRNC33L539", "idNo": "******************", "name": "测试史珊", "englishName": "Nancy Connie Mills", "bankCard": "****************", "phone": "***********", "orgName": "esigntest史珊经营的个体工商户"}]}
2025-10-09 14:54:11,547 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:54:11,547 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:54:11,547 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:54:13,846 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:54:13,847 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:54:13,848 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:54:13,879 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:54:13,879 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:54:13,879 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:54:15,991 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:54:15,991 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:54:15,992 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:54:16,026 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:54:16,026 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:54:16,026 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:54:18,114 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:54:18,114 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:54:18,114 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:55:44,984 - main - INFO - 🛑 应用关闭中...
2025-10-09 14:55:44,991 - main - INFO - ✅ 连接状态已保存
2025-10-09 14:55:44,992 - main - INFO - ✅ 应用已关闭
2025-10-09 14:55:57,932 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:55:57,933 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:55:58,796 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-09 14:55:58,974 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-09 14:55:58,974 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '平台功能', '意愿域', '费用域', 'SaaS域', '实名域', '签署域', 'wiki域', '证书域']
2025-10-09 14:55:58,975 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:55:58,975 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:55:58,975 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:55:58,980 - __main__ - INFO - certificate路由加载成功
2025-10-09 14:55:58,982 - __main__ - INFO - fee路由加载成功
2025-10-09 14:55:58,983 - __main__ - INFO - file路由加载成功
2025-10-09 14:55:58,985 - __main__ - INFO - identity路由加载成功
2025-10-09 14:55:58,989 - __main__ - INFO - intention路由加载成功
2025-10-09 14:55:58,999 - __main__ - INFO - platform路由加载成功
2025-10-09 14:55:59,008 - __main__ - INFO - saas路由加载成功
2025-10-09 14:55:59,017 - __main__ - INFO - signing路由加载成功
2025-10-09 14:55:59,018 - __main__ - INFO - wiki路由加载成功
2025-10-09 14:55:59,020 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-09 14:55:59,020 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '平台功能', '意愿域', '费用域', 'SaaS域', '实名域', '签署域', 'wiki域', '证书域']
2025-10-09 14:55:59,055 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:55:59,055 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:55:59,055 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:55:59,055 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:55:59,056 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-09 14:55:59,056 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-09 14:55:59,056 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:55:59,056 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-09 14:55:59,056 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-09 14:56:00,756 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:56:00,756 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:56:01,634 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-09 14:56:01,835 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-09 14:56:01,836 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '意愿域', '实名域', 'wiki域', 'SaaS域', '签署域', '证书域', '费用域', '文件域']
2025-10-09 14:56:01,836 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:56:01,836 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:56:01,836 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:56:01,842 - __mp_main__ - INFO - certificate路由加载成功
2025-10-09 14:56:01,844 - __mp_main__ - INFO - fee路由加载成功
2025-10-09 14:56:01,845 - __mp_main__ - INFO - file路由加载成功
2025-10-09 14:56:01,847 - __mp_main__ - INFO - identity路由加载成功
2025-10-09 14:56:01,851 - __mp_main__ - INFO - intention路由加载成功
2025-10-09 14:56:01,861 - __mp_main__ - INFO - platform路由加载成功
2025-10-09 14:56:01,870 - __mp_main__ - INFO - saas路由加载成功
2025-10-09 14:56:01,878 - __mp_main__ - INFO - signing路由加载成功
2025-10-09 14:56:01,880 - __mp_main__ - INFO - wiki路由加载成功
2025-10-09 14:56:01,883 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-09 14:56:01,883 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '意愿域', '实名域', 'wiki域', 'SaaS域', '签署域', '证书域', '费用域', '文件域']
2025-10-09 14:56:01,921 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:56:01,921 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:56:01,921 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:56:01,921 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:56:01,986 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 14:56:01,986 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 14:56:01,986 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '意愿域', '实名域', 'wiki域', 'SaaS域', '签署域', '证书域', '费用域', '文件域']
2025-10-09 14:56:01,987 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 14:56:01,987 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 14:56:01,988 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 14:56:01,993 - main - INFO - certificate路由加载成功
2025-10-09 14:56:01,995 - main - INFO - fee路由加载成功
2025-10-09 14:56:01,996 - main - INFO - file路由加载成功
2025-10-09 14:56:01,998 - main - INFO - identity路由加载成功
2025-10-09 14:56:02,002 - main - INFO - intention路由加载成功
2025-10-09 14:56:02,016 - main - INFO - platform路由加载成功
2025-10-09 14:56:02,025 - main - INFO - saas路由加载成功
2025-10-09 14:56:02,034 - main - INFO - signing路由加载成功
2025-10-09 14:56:02,036 - main - INFO - wiki路由加载成功
2025-10-09 14:56:02,037 - main - INFO - 🚀 设置MCP服务...
2025-10-09 14:56:02,037 - main - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '意愿域', '实名域', 'wiki域', 'SaaS域', '签署域', '证书域', '费用域', '文件域']
2025-10-09 14:56:02,128 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 14:56:02,129 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 14:56:02,129 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 14:56:02,129 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 14:56:02,130 - main - INFO - 🚀 应用启动中...
2025-10-09 14:56:02,130 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-09 14:56:02,131 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-09 14:56:02,131 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-09 14:56:02,131 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-09 14:56:02,131 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-09 14:56:02,131 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-09 14:56:06,927 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:56:06,927 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:56:06,927 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:56:08,822 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:56:08,823 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:56:08,823 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000MKRJEPFL12", "idNo": "310112196710037352", "name": "测试孙轩", "englishName": "Gary Perry", "bankCard": "6235800019593569484", "phone": "***********", "orgName": "esigntest孙轩经营的个体工商户"}]}
2025-10-09 14:56:08,859 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:56:08,859 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:56:08,859 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:56:10,628 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:56:10,629 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:56:10,629 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:56:10,714 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:56:10,714 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:56:10,714 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:56:12,580 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:56:12,581 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:56:12,583 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:56:12,617 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:56:12,617 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:56:12,617 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:56:14,629 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:56:14,630 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:56:14,630 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:58:05,008 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:58:05,008 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:05,008 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:58:09,458 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:58:09,458 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:58:09,458 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000TQAR425H7U", "idNo": "511500194708092139", "name": "测试缪宏康", "englishName": "Alvin Henry Wells", "bankCard": "****************", "phone": "***********", "orgName": "esigntest缪宏康经营的个体工商户"}]}
2025-10-09 14:58:09,499 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:58:09,499 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:09,500 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:58:11,921 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:58:11,921 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:58:11,921 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:58:11,961 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:58:11,961 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:11,961 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:58:15,487 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:58:15,488 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:58:15,488 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:58:15,619 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:58:15,619 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:15,620 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:58:17,709 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:58:17,714 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:58:17,714 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:58:42,635 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:58:42,635 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:42,635 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:58:44,375 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:58:44,376 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:58:44,376 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "910000004FQT4X069A", "idNo": "110117199809040904", "name": "测试查紫慧", "englishName": "Anna Stevens", "bankCard": "6214491024489683000", "phone": "***********", "orgName": "esigntest查紫慧经营的个体工商户"}]}
2025-10-09 14:58:52,077 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:58:52,077 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:52,077 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:58:53,845 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:58:53,845 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:58:53,846 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:58:53,891 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:58:53,891 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:53,891 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:58:55,742 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:58:55,743 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:58:55,743 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:58:55,775 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:58:55,775 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:58:55,775 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:58:59,100 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:58:59,102 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:58:59,102 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:59:14,585 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:59:14,585 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:14,585 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:59:16,553 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:59:16,558 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:59:16,558 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "9100000012CXRH182Q", "idNo": "500229195210144294", "name": "测试孙辉有", "englishName": "Jon Andrew Butler", "bankCard": "****************", "phone": "***********", "orgName": "esigntest孙辉有经营的个体工商户"}]}
2025-10-09 14:59:16,614 - app.mcpController.domains.signing_controller - INFO - 一键签署请求: env=test, flow_id=test_flow_id, group=DEFAULT, type=SIGN
2025-10-09 14:59:16,614 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/sign/start
2025-10-09 14:59:16,614 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:16,615 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "flowId": "test_flow_id", "group": "DEFAULT", "type": "SIGN"}
2025-10-09 14:59:21,653 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/sign/start "HTTP/1.1 200 "
2025-10-09 14:59:21,655 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:59:21,655 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "流程异常，已进行过期！"}
2025-10-09 14:59:21,660 - app.mcpController.domains.signing_controller - INFO - 一键签署完成: {'status': 'error', 'message': '流程异常，已进行过期！', 'timestamp': '2025-10-09T14:59:21.660160', 'details': {'success': False, 'message': '流程异常，已进行过期！', 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 06:59:21 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 06:59:21 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}
2025-10-09 14:59:21,691 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:59:21,691 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:21,691 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 14:59:23,446 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 14:59:23,447 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 14:59:23,447 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 14:59:23,475 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/fee/app
2025-10-09 14:59:23,475 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6", "operator": "mcp"}
2025-10-09 14:59:23,476 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "name": null}
2025-10-09 14:59:25,406 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/fee/app "HTTP/1.1 200 "
2025-10-09 14:59:25,406 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:59:25,406 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "gid和名字不能同时为空"}
2025-10-09 14:59:25,437 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 14:59:25,437 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:25,438 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 14:59:27,349 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:59:27,349 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:59:27,349 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:59:27,376 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 14:59:27,376 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:27,376 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 14:59:29,245 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 14:59:29,245 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 14:59:29,245 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 14:59:51,465 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 14:59:51,465 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:51,465 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 14:59:53,959 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 14:59:53,960 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:59:53,960 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000RHWNU6K996", "idNo": "******************", "name": "测试殷瑶英", "englishName": "Joann Kelly Diaz", "bankCard": "****************", "phone": "***********", "orgName": "esigntest殷瑶英经营的个体工商户"}]}
2025-10-09 14:59:53,986 - app.mcpController.domains.signing_controller - INFO - 一键签署请求: env=test, flow_id=test_flow_id, group=DEFAULT, type=SIGN
2025-10-09 14:59:53,986 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/sign/start
2025-10-09 14:59:53,986 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:53,986 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "flowId": "test_flow_id", "group": "DEFAULT", "type": "SIGN"}
2025-10-09 14:59:58,951 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/sign/start "HTTP/1.1 200 "
2025-10-09 14:59:58,951 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 14:59:58,951 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "流程异常，已进行过期！"}
2025-10-09 14:59:58,955 - app.mcpController.domains.signing_controller - INFO - 一键签署完成: {'status': 'error', 'message': '流程异常，已进行过期！', 'timestamp': '2025-10-09T14:59:58.954729', 'details': {'success': False, 'message': '流程异常，已进行过期！', 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 06:59:58 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 06:59:58 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}
2025-10-09 14:59:58,993 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 14:59:58,994 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 14:59:58,994 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 15:00:00,799 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 15:00:00,799 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 15:00:00,799 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 15:00:00,824 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/fee/app
2025-10-09 15:00:00,824 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6", "operator": "mcp"}
2025-10-09 15:00:00,824 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "name": null}
2025-10-09 15:00:02,659 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/fee/app "HTTP/1.1 200 "
2025-10-09 15:00:02,660 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:00:02,660 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "gid和名字不能同时为空"}
2025-10-09 15:00:02,697 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 15:00:02,698 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:02,698 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 15:00:04,653 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:00:04,653 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:00:04,654 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:00:04,686 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 15:00:04,687 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:04,687 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 15:00:07,078 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:00:07,079 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:00:07,079 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:00:22,014 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 15:00:22,015 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:22,015 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 15:00:24,012 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 15:00:24,016 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:00:24,016 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000KBD6Q4K48H", "idNo": "360521199411186527", "name": "测试亓羽", "englishName": "Joyce Audrey Bryant", "bankCard": "6213531119583046239", "phone": "***********", "orgName": "esigntest亓羽经营的个体工商户"}]}
2025-10-09 15:00:24,049 - app.mcpController.domains.signing_controller - INFO - 一键签署请求: env=test, flow_id=test_flow_id, group=DEFAULT, type=SIGN
2025-10-09 15:00:24,050 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/sign/start
2025-10-09 15:00:24,050 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:24,050 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "flowId": "test_flow_id", "group": "DEFAULT", "type": "SIGN"}
2025-10-09 15:00:29,041 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/sign/start "HTTP/1.1 200 "
2025-10-09 15:00:29,043 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:00:29,044 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "流程异常，已进行过期！"}
2025-10-09 15:00:29,051 - app.mcpController.domains.signing_controller - INFO - 一键签署完成: {'status': 'error', 'message': '流程异常，已进行过期！', 'timestamp': '2025-10-09T15:00:29.051888', 'details': {'success': False, 'message': '流程异常，已进行过期！', 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 07:00:29 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 07:00:29 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}
2025-10-09 15:00:29,081 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 15:00:29,081 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:29,081 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 15:00:30,931 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 15:00:30,932 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 15:00:30,932 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 15:00:30,966 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/fee/app
2025-10-09 15:00:30,967 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6", "operator": "mcp"}
2025-10-09 15:00:30,967 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "name": null}
2025-10-09 15:00:33,077 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/fee/app "HTTP/1.1 200 "
2025-10-09 15:00:33,078 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:00:33,078 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "gid和名字不能同时为空"}
2025-10-09 15:00:33,105 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 15:00:33,105 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:33,105 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 15:00:35,129 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:00:35,129 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:00:35,129 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:00:35,157 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 15:00:35,157 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:00:35,158 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 15:00:37,085 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:00:37,086 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:00:37,086 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:00:56,407 - main - INFO - 🛑 应用关闭中...
2025-10-09 15:00:56,408 - main - INFO - ✅ 连接状态已保存
2025-10-09 15:00:56,408 - main - INFO - ✅ 应用已关闭
2025-10-09 15:00:58,447 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 15:00:58,448 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 15:00:59,410 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-09 15:00:59,602 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-09 15:00:59,603 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '文件域', '实名域', '平台功能', 'SaaS域', '意愿域', '费用域', '签署域', '证书域']
2025-10-09 15:00:59,603 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 15:00:59,603 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 15:00:59,603 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 15:00:59,608 - __mp_main__ - INFO - certificate路由加载成功
2025-10-09 15:00:59,610 - __mp_main__ - INFO - fee路由加载成功
2025-10-09 15:00:59,611 - __mp_main__ - INFO - file路由加载成功
2025-10-09 15:00:59,613 - __mp_main__ - INFO - identity路由加载成功
2025-10-09 15:00:59,618 - __mp_main__ - INFO - intention路由加载成功
2025-10-09 15:00:59,631 - __mp_main__ - INFO - platform路由加载成功
2025-10-09 15:00:59,638 - __mp_main__ - INFO - saas路由加载成功
2025-10-09 15:00:59,647 - __mp_main__ - INFO - signing路由加载成功
2025-10-09 15:00:59,648 - __mp_main__ - INFO - wiki路由加载成功
2025-10-09 15:00:59,650 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-09 15:00:59,650 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '文件域', '实名域', '平台功能', 'SaaS域', '意愿域', '费用域', '签署域', '证书域']
2025-10-09 15:00:59,684 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 15:00:59,684 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 15:00:59,684 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 15:00:59,684 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 15:00:59,746 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-09 15:00:59,746 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-09 15:00:59,746 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '文件域', '实名域', '平台功能', 'SaaS域', '意愿域', '费用域', '签署域', '证书域']
2025-10-09 15:00:59,747 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-09 15:00:59,747 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-09 15:00:59,747 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-09 15:00:59,753 - main - INFO - certificate路由加载成功
2025-10-09 15:00:59,755 - main - INFO - fee路由加载成功
2025-10-09 15:00:59,756 - main - INFO - file路由加载成功
2025-10-09 15:00:59,758 - main - INFO - identity路由加载成功
2025-10-09 15:00:59,762 - main - INFO - intention路由加载成功
2025-10-09 15:00:59,773 - main - INFO - platform路由加载成功
2025-10-09 15:00:59,781 - main - INFO - saas路由加载成功
2025-10-09 15:00:59,793 - main - INFO - signing路由加载成功
2025-10-09 15:00:59,794 - main - INFO - wiki路由加载成功
2025-10-09 15:00:59,796 - main - INFO - 🚀 设置MCP服务...
2025-10-09 15:00:59,796 - main - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '文件域', '实名域', '平台功能', 'SaaS域', '意愿域', '费用域', '签署域', '证书域']
2025-10-09 15:00:59,869 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-09 15:00:59,869 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-09 15:00:59,869 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-09 15:00:59,869 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-09 15:00:59,869 - main - INFO - 🚀 应用启动中...
2025-10-09 15:00:59,870 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-09 15:00:59,870 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-09 15:00:59,870 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-09 15:00:59,870 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-09 15:00:59,870 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-09 15:00:59,870 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-09 15:01:00,817 - mcpService.common.http_client - INFO - HTTP请求: GET http://sdk.testk8s.tsign.cn/random/get
2025-10-09 15:01:00,818 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:01:00,818 - mcpService.common.http_client - INFO - 请求数据: {"mock": true}
2025-10-09 15:01:02,650 - httpx - INFO - HTTP Request: GET http://sdk.testk8s.tsign.cn/random/get?mock=true "HTTP/1.1 200 "
2025-10-09 15:01:02,651 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:01:02,651 - mcpService.common.http_client - INFO - 响应数据: {"success": true, "accountList": [{"orgCode": "91000000JRCDQWXMXP", "idNo": "350725198706161220", "name": "测试里瑞", "englishName": "Debra Annie Diaz", "bankCard": "6236070158298278339", "phone": "***********", "orgName": "esigntest里瑞经营的个体工商户"}]}
2025-10-09 15:01:02,687 - app.mcpController.domains.signing_controller - INFO - 一键签署请求: env=test, flow_id=test_flow_id, group=DEFAULT, type=SIGN
2025-10-09 15:01:02,687 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/sign/start
2025-10-09 15:01:02,687 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:01:02,687 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "flowId": "test_flow_id", "group": "DEFAULT", "type": "SIGN"}
2025-10-09 15:01:07,721 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/sign/start "HTTP/1.1 200 "
2025-10-09 15:01:07,721 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:01:07,721 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "流程异常，已进行过期！"}
2025-10-09 15:01:07,724 - app.mcpController.domains.signing_controller - INFO - 一键签署完成: {'status': 'error', 'message': '流程异常，已进行过期！', 'timestamp': '2025-10-09T15:01:07.724621', 'details': {'success': False, 'message': '流程异常，已进行过期！', 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 07:01:07 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://sdk.smlk8s.esign.cn/sign/start', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'env': 'test', 'flowId': 'test_flow_id', 'group': 'DEFAULT', 'type': 'SIGN'}, 'params': None, 'environment': 'test', 'domain': 'signing'}, 'response_details': {'status_code': 200, 'headers': {'connection': 'close', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'content-type': 'application/json', 'date': 'Thu, 09 Oct 2025 07:01:07 GMT', 'vary': 'Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers'}, 'response_time': 'N/A'}}
2025-10-09 15:01:07,766 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId
2025-10-09 15:01:07,766 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "test_app_id", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:01:07,766 - mcpService.common.http_client - INFO - 请求数据: {"idNo": "110101199003072117", "mobile": "***********", "name": "张三", "thirdPartyUserId": "test_user_001"}
2025-10-09 15:01:09,679 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId "HTTP/1.1 401 Unauthorized"
2025-10-09 15:01:09,680 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-10-09 15:01:09,680 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-10-09 15:01:09,721 - mcpService.common.http_client - INFO - HTTP请求: POST http://sdk.smlk8s.esign.cn/fee/app
2025-10-09 15:01:09,722 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6", "operator": "mcp"}
2025-10-09 15:01:09,722 - mcpService.common.http_client - INFO - 请求数据: {"env": "test", "name": null}
2025-10-09 15:01:11,695 - httpx - INFO - HTTP Request: POST http://sdk.smlk8s.esign.cn/fee/app "HTTP/1.1 200 "
2025-10-09 15:01:11,697 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-10-09 15:01:11,697 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "message": "gid和名字不能同时为空"}
2025-10-09 15:01:11,721 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/identity/verification/create
2025-10-09 15:01:11,721 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:01:11,721 - mcpService.common.http_client - INFO - 请求数据: {"name": "张三", "idcard": "110101199003072117", "mobile": "***********", "verificationType": "BASIC"}
2025-10-09 15:01:13,670 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/identity/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:01:13,670 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:01:13,671 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:01:13,703 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-test-openapi.tsign.cn/intention/verification/create
2025-10-09 15:01:13,703 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-10-09 15:01:13,703 - mcpService.common.http_client - INFO - 请求数据: {"signerName": "张三", "signerMobile": "***********", "signerIdcard": "110101199003072117", "documentTitle": "测试文档", "verificationType": "SMS"}
2025-10-09 15:01:15,647 - httpx - INFO - HTTP Request: POST http://in-test-openapi.tsign.cn/intention/verification/create "HTTP/1.1 404 Not Found"
2025-10-09 15:01:15,647 - mcpService.common.http_client - INFO - HTTP响应: 404
2025-10-09 15:01:15,647 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 404, "message": "NOT_FOUND"}
2025-10-09 15:02:01,493 - main - INFO - 🛑 应用关闭中...
2025-10-09 15:02:01,495 - main - INFO - ✅ 连接状态已保存
2025-10-09 15:02:01,495 - main - INFO - ✅ 应用已关闭
