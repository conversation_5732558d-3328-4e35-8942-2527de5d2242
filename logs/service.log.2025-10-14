2025-10-14 16:14:05,361 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:14:05,364 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:14:06,313 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 16:14:06,540 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 16:14:06,541 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '费用域', '签署域', '平台功能', 'SaaS域', '实名域', 'wiki域', '证书域', '文件域']
2025-10-14 16:14:06,541 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:14:06,541 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:14:06,541 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:14:06,551 - __main__ - INFO - certificate路由加载成功
2025-10-14 16:14:06,554 - __main__ - INFO - fee路由加载成功
2025-10-14 16:14:06,556 - __main__ - INFO - file路由加载成功
2025-10-14 16:14:06,559 - __main__ - INFO - identity路由加载成功
2025-10-14 16:14:06,567 - __main__ - INFO - intention路由加载成功
2025-10-14 16:14:06,582 - __main__ - INFO - platform路由加载成功
2025-10-14 16:14:06,591 - __main__ - INFO - saas路由加载成功
2025-10-14 16:14:06,600 - __main__ - INFO - signing路由加载成功
2025-10-14 16:14:06,602 - __main__ - INFO - wiki路由加载成功
2025-10-14 16:14:06,604 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 16:14:06,604 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '费用域', '签署域', '平台功能', 'SaaS域', '实名域', 'wiki域', '证书域', '文件域']
2025-10-14 16:14:06,646 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:14:06,647 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:14:06,647 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:14:06,647 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:14:06,647 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 16:14:06,647 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 16:14:06,647 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:14:06,647 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 16:14:06,647 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 16:14:08,455 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:14:08,455 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:14:09,349 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 16:14:09,532 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 16:14:09,532 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '实名域', 'wiki域', '平台功能', '签署域', '意愿域', 'SaaS域', '费用域', '文件域']
2025-10-14 16:14:09,532 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:14:09,532 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:14:09,532 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:14:09,537 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 16:14:09,539 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 16:14:09,540 - __mp_main__ - INFO - file路由加载成功
2025-10-14 16:14:09,542 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 16:14:09,547 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 16:14:09,559 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 16:14:09,571 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 16:14:09,585 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 16:14:09,587 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 16:14:09,590 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 16:14:09,590 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '实名域', 'wiki域', '平台功能', '签署域', '意愿域', 'SaaS域', '费用域', '文件域']
2025-10-14 16:14:09,638 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:14:09,638 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:14:09,638 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:14:09,638 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:14:09,741 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:14:09,742 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:14:09,742 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '实名域', 'wiki域', '平台功能', '签署域', '意愿域', 'SaaS域', '费用域', '文件域']
2025-10-14 16:14:09,743 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:14:09,743 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:14:09,743 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:14:09,750 - main - INFO - certificate路由加载成功
2025-10-14 16:14:09,753 - main - INFO - fee路由加载成功
2025-10-14 16:14:09,754 - main - INFO - file路由加载成功
2025-10-14 16:14:09,757 - main - INFO - identity路由加载成功
2025-10-14 16:14:09,764 - main - INFO - intention路由加载成功
2025-10-14 16:14:09,776 - main - INFO - platform路由加载成功
2025-10-14 16:14:09,787 - main - INFO - saas路由加载成功
2025-10-14 16:14:09,802 - main - INFO - signing路由加载成功
2025-10-14 16:14:09,804 - main - INFO - wiki路由加载成功
2025-10-14 16:14:09,805 - main - INFO - 🚀 设置MCP服务...
2025-10-14 16:14:09,806 - main - INFO - 🏷️ 使用自动发现的标签: ['证书域', '实名域', 'wiki域', '平台功能', '签署域', '意愿域', 'SaaS域', '费用域', '文件域']
2025-10-14 16:14:09,916 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:14:09,916 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:14:09,916 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:14:09,916 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:14:09,916 - main - INFO - 🚀 应用启动中...
2025-10-14 16:14:09,917 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 16:14:09,917 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 16:14:09,917 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 16:14:09,917 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 16:14:09,917 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 16:14:09,917 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 16:15:15,856 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-14 16:15:35,860 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:15:55,862 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:16:15,858 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:16:35,861 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:16:55,856 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:17:15,865 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:17:35,858 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:17:55,864 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:18:15,857 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:18:35,858 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:18:55,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:19:15,870 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:19:35,861 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:19:55,863 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:20:15,864 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:20:35,858 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:20:55,866 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:21:15,866 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:21:35,875 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:21:55,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:22:15,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:22:35,865 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:22:55,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:23:15,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:23:35,870 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:23:55,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:24:15,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:24:35,865 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:24:55,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:25:15,869 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:25:35,866 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:25:55,869 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:26:15,859 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:26:35,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:26:55,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:27:15,869 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:27:35,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:27:55,870 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:28:15,860 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:28:35,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:28:55,864 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:29:15,874 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:29:35,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:29:55,865 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:30:15,873 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:30:35,873 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:30:55,866 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:31:15,870 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:31:35,873 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:31:55,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:32:15,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:32:35,874 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:32:55,861 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:33:15,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:33:35,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:33:55,874 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:34:15,871 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:34:35,876 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:34:55,872 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:35:15,869 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:35:35,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:35:55,867 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:36:15,869 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:36:35,868 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:36:55,861 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:37:15,864 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:37:18,362 - main - INFO - 🛑 应用关闭中...
2025-10-14 16:37:18,364 - main - INFO - ✅ 连接状态已保存
2025-10-14 16:37:18,364 - main - INFO - ✅ 应用已关闭
2025-10-14 16:39:41,074 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:39:41,074 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:39:41,883 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 16:39:42,054 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 16:39:42,054 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '费用域', 'SaaS域', '平台功能', '意愿域', '文件域', '证书域', 'wiki域', '签署域']
2025-10-14 16:39:42,055 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:39:42,055 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:39:42,055 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:39:42,060 - __main__ - INFO - certificate路由加载成功
2025-10-14 16:39:42,062 - __main__ - INFO - fee路由加载成功
2025-10-14 16:39:42,063 - __main__ - INFO - file路由加载成功
2025-10-14 16:39:42,064 - __main__ - INFO - identity路由加载成功
2025-10-14 16:39:42,069 - __main__ - INFO - intention路由加载成功
2025-10-14 16:39:42,080 - __main__ - INFO - platform路由加载成功
2025-10-14 16:39:42,088 - __main__ - INFO - saas路由加载成功
2025-10-14 16:39:42,099 - __main__ - INFO - signing路由加载成功
2025-10-14 16:39:42,101 - __main__ - INFO - wiki路由加载成功
2025-10-14 16:39:42,104 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 16:39:42,104 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', '费用域', 'SaaS域', '平台功能', '意愿域', '文件域', '证书域', 'wiki域', '签署域']
2025-10-14 16:39:42,139 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:39:42,139 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:39:42,140 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:39:42,140 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:39:42,140 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 16:39:42,140 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 16:39:42,140 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:39:42,140 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 16:39:42,140 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 16:39:43,936 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:39:43,941 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:39:44,978 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 16:39:45,211 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 16:39:45,211 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '费用域', 'SaaS域', '平台功能', '实名域', '签署域', 'wiki域', '证书域', '文件域']
2025-10-14 16:39:45,211 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:39:45,211 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:39:45,211 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:39:45,218 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 16:39:45,221 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 16:39:45,223 - __mp_main__ - INFO - file路由加载成功
2025-10-14 16:39:45,225 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 16:39:45,230 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 16:39:45,247 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 16:39:45,258 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 16:39:45,269 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 16:39:45,272 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 16:39:45,274 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 16:39:45,274 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '费用域', 'SaaS域', '平台功能', '实名域', '签署域', 'wiki域', '证书域', '文件域']
2025-10-14 16:39:45,316 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:39:45,317 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:39:45,317 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:39:45,317 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:39:45,389 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 16:39:45,389 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 16:39:45,389 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '费用域', 'SaaS域', '平台功能', '实名域', '签署域', 'wiki域', '证书域', '文件域']
2025-10-14 16:39:45,391 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 16:39:45,391 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 16:39:45,391 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 16:39:45,397 - main - INFO - certificate路由加载成功
2025-10-14 16:39:45,399 - main - INFO - fee路由加载成功
2025-10-14 16:39:45,400 - main - INFO - file路由加载成功
2025-10-14 16:39:45,402 - main - INFO - identity路由加载成功
2025-10-14 16:39:45,409 - main - INFO - intention路由加载成功
2025-10-14 16:39:45,424 - main - INFO - platform路由加载成功
2025-10-14 16:39:45,435 - main - INFO - saas路由加载成功
2025-10-14 16:39:45,457 - main - INFO - signing路由加载成功
2025-10-14 16:39:45,463 - main - INFO - wiki路由加载成功
2025-10-14 16:39:45,466 - main - INFO - 🚀 设置MCP服务...
2025-10-14 16:39:45,467 - main - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '费用域', 'SaaS域', '平台功能', '实名域', '签署域', 'wiki域', '证书域', '文件域']
2025-10-14 16:39:45,583 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 16:39:45,584 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 16:39:45,584 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 16:39:45,584 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 16:39:45,584 - main - INFO - 🚀 应用启动中...
2025-10-14 16:39:45,585 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 16:39:45,585 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 16:39:45,585 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 16:39:45,585 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 16:39:45,585 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 16:39:45,585 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 16:39:55,895 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-14 16:40:15,898 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:40:35,906 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:40:55,909 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:41:15,899 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:41:35,902 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:41:55,898 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:42:15,900 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:42:35,906 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-14 16:42:41,298 - main - INFO - 🛑 应用关闭中...
2025-10-14 16:42:41,300 - main - INFO - ✅ 连接状态已保存
2025-10-14 16:42:41,300 - main - INFO - ✅ 应用已关闭
2025-10-14 17:11:52,747 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:11:52,747 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:11:53,804 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:11:53,980 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:11:53,980 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '实名域', '证书域', '文件域', 'SaaS域', '签署域', 'wiki域', '意愿域', '费用域']
2025-10-14 17:11:53,980 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:11:53,980 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:11:53,980 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:11:53,986 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:11:53,988 - __main__ - INFO - fee路由加载成功
2025-10-14 17:11:53,990 - __main__ - INFO - file路由加载成功
2025-10-14 17:11:53,992 - __main__ - INFO - identity路由加载成功
2025-10-14 17:11:53,996 - __main__ - INFO - intention路由加载成功
2025-10-14 17:11:54,006 - __main__ - INFO - platform路由加载成功
2025-10-14 17:11:54,013 - __main__ - INFO - saas路由加载成功
2025-10-14 17:11:54,026 - __main__ - INFO - signing路由加载成功
2025-10-14 17:11:54,029 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:11:54,032 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:11:54,032 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '实名域', '证书域', '文件域', 'SaaS域', '签署域', 'wiki域', '意愿域', '费用域']
2025-10-14 17:11:54,069 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:11:54,069 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:11:54,069 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:11:54,069 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:11:54,069 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:11:54,070 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:11:54,070 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:11:54,070 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:11:54,070 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:11:55,707 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:11:55,708 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:11:56,707 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:11:56,901 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:11:56,901 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', 'wiki域', '费用域', '平台功能', 'SaaS域', '签署域', '实名域', '证书域']
2025-10-14 17:11:56,902 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:11:56,902 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:11:56,902 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:11:56,907 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:11:56,909 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:11:56,911 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:11:56,914 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:11:56,920 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:11:56,936 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:11:56,947 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:11:56,957 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:11:56,959 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:11:56,961 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:11:56,962 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', 'wiki域', '费用域', '平台功能', 'SaaS域', '签署域', '实名域', '证书域']
2025-10-14 17:11:56,996 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:11:56,997 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:11:56,997 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:11:56,997 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:11:57,063 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:11:57,064 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:11:57,064 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', 'wiki域', '费用域', '平台功能', 'SaaS域', '签署域', '实名域', '证书域']
2025-10-14 17:11:57,065 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:11:57,065 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:11:57,065 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:11:57,071 - main - INFO - certificate路由加载成功
2025-10-14 17:11:57,073 - main - INFO - fee路由加载成功
2025-10-14 17:11:57,074 - main - INFO - file路由加载成功
2025-10-14 17:11:57,075 - main - INFO - identity路由加载成功
2025-10-14 17:11:57,080 - main - INFO - intention路由加载成功
2025-10-14 17:11:57,091 - main - INFO - platform路由加载成功
2025-10-14 17:11:57,099 - main - INFO - saas路由加载成功
2025-10-14 17:11:57,109 - main - INFO - signing路由加载成功
2025-10-14 17:11:57,110 - main - INFO - wiki路由加载成功
2025-10-14 17:11:57,112 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:11:57,112 - main - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', 'wiki域', '费用域', '平台功能', 'SaaS域', '签署域', '实名域', '证书域']
2025-10-14 17:11:57,212 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:11:57,212 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:11:57,212 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:11:57,212 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:11:57,213 - main - INFO - 🚀 应用启动中...
2025-10-14 17:11:57,213 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:11:57,214 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:11:57,214 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:11:57,214 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:11:57,214 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:11:57,214 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:13:01,581 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:13:01,583 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:13:01,583 - main - INFO - ✅ 应用已关闭
2025-10-14 17:17:51,186 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:17:51,186 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:17:52,180 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:17:52,352 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:17:52,353 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '实名域', 'SaaS域', '平台功能', 'wiki域', '签署域', '证书域', '费用域', '文件域']
2025-10-14 17:17:52,353 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:17:52,353 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:17:52,353 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:17:52,359 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:17:52,361 - __main__ - INFO - fee路由加载成功
2025-10-14 17:17:52,362 - __main__ - INFO - file路由加载成功
2025-10-14 17:17:52,364 - __main__ - INFO - identity路由加载成功
2025-10-14 17:17:52,370 - __main__ - INFO - intention路由加载成功
2025-10-14 17:17:52,380 - __main__ - INFO - platform路由加载成功
2025-10-14 17:17:52,388 - __main__ - INFO - saas路由加载成功
2025-10-14 17:17:52,403 - __main__ - INFO - signing路由加载成功
2025-10-14 17:17:52,407 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:17:52,411 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:17:52,412 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '实名域', 'SaaS域', '平台功能', 'wiki域', '签署域', '证书域', '费用域', '文件域']
2025-10-14 17:17:52,450 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:17:52,450 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:17:52,450 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:17:52,450 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:17:52,450 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:17:52,450 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:17:52,450 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:17:52,450 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:17:52,450 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:17:54,127 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:17:54,128 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:17:55,008 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:17:55,199 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:17:55,199 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '意愿域', '文件域', '费用域', 'SaaS域', '证书域', '签署域', '实名域', '平台功能']
2025-10-14 17:17:55,199 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:17:55,199 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:17:55,199 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:17:55,205 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:17:55,207 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:17:55,208 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:17:55,210 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:17:55,215 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:17:55,228 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:17:55,240 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:17:55,252 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:17:55,254 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:17:55,258 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:17:55,258 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '意愿域', '文件域', '费用域', 'SaaS域', '证书域', '签署域', '实名域', '平台功能']
2025-10-14 17:17:55,300 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:17:55,300 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:17:55,300 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:17:55,301 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:17:55,369 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:17:55,370 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:17:55,370 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '意愿域', '文件域', '费用域', 'SaaS域', '证书域', '签署域', '实名域', '平台功能']
2025-10-14 17:17:55,371 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:17:55,371 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:17:55,371 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:17:55,377 - main - INFO - certificate路由加载成功
2025-10-14 17:17:55,380 - main - INFO - fee路由加载成功
2025-10-14 17:17:55,381 - main - INFO - file路由加载成功
2025-10-14 17:17:55,383 - main - INFO - identity路由加载成功
2025-10-14 17:17:55,387 - main - INFO - intention路由加载成功
2025-10-14 17:17:55,397 - main - INFO - platform路由加载成功
2025-10-14 17:17:55,404 - main - INFO - saas路由加载成功
2025-10-14 17:17:55,412 - main - INFO - signing路由加载成功
2025-10-14 17:17:55,414 - main - INFO - wiki路由加载成功
2025-10-14 17:17:55,415 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:17:55,415 - main - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '意愿域', '文件域', '费用域', 'SaaS域', '证书域', '签署域', '实名域', '平台功能']
2025-10-14 17:17:55,524 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:17:55,524 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:17:55,524 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:17:55,524 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:17:55,525 - main - INFO - 🚀 应用启动中...
2025-10-14 17:17:55,525 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:17:55,526 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:17:55,526 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:17:55,526 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:17:55,526 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:17:55,526 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:18:11,033 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:18:11,034 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:18:11,035 - main - INFO - ✅ 应用已关闭
2025-10-14 17:23:06,087 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:06,088 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:06,240 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:23:06,430 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:23:06,430 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '平台功能', 'wiki域', '文件域', '费用域', 'SaaS域', '意愿域', '实名域', '签署域']
2025-10-14 17:23:06,430 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:06,430 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:06,430 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:06,435 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:23:06,437 - __main__ - INFO - fee路由加载成功
2025-10-14 17:23:06,439 - __main__ - INFO - file路由加载成功
2025-10-14 17:23:06,441 - __main__ - INFO - identity路由加载成功
2025-10-14 17:23:06,445 - __main__ - INFO - intention路由加载成功
2025-10-14 17:23:06,455 - __main__ - INFO - platform路由加载成功
2025-10-14 17:23:06,463 - __main__ - INFO - saas路由加载成功
2025-10-14 17:23:06,472 - __main__ - INFO - signing路由加载成功
2025-10-14 17:23:06,474 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:23:06,476 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:06,476 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '平台功能', 'wiki域', '文件域', '费用域', 'SaaS域', '意愿域', '实名域', '签署域']
2025-10-14 17:23:06,556 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:06,556 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:06,556 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:06,556 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:06,572 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:23:06,572 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:23:06,575 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:23:09,121 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:09,121 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:09,270 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:23:09,468 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:23:09,468 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '证书域', '实名域', '意愿域', 'wiki域', '签署域', 'SaaS域', '费用域', '平台功能']
2025-10-14 17:23:09,468 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:09,468 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:09,468 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:09,474 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:23:09,476 - __main__ - INFO - fee路由加载成功
2025-10-14 17:23:09,478 - __main__ - INFO - file路由加载成功
2025-10-14 17:23:09,480 - __main__ - INFO - identity路由加载成功
2025-10-14 17:23:09,485 - __main__ - INFO - intention路由加载成功
2025-10-14 17:23:09,496 - __main__ - INFO - platform路由加载成功
2025-10-14 17:23:09,504 - __main__ - INFO - saas路由加载成功
2025-10-14 17:23:09,513 - __main__ - INFO - signing路由加载成功
2025-10-14 17:23:09,515 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:23:09,517 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:09,517 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '证书域', '实名域', '意愿域', 'wiki域', '签署域', 'SaaS域', '费用域', '平台功能']
2025-10-14 17:23:09,596 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:09,596 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:09,596 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:09,596 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:09,606 - werkzeug - WARNING -  * Debugger is active!
2025-10-14 17:23:09,608 - werkzeug - INFO -  * Debugger PIN: 453-144-825
2025-10-14 17:23:36,092 - werkzeug - WARNING -  * Debugger is active!
2025-10-14 17:23:36,092 - werkzeug - INFO -  * Debugger PIN: 453-144-825
2025-10-14 17:23:36,341 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:23:36,342 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:23:36,342 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:23:38,817 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:38,818 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:38,948 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:23:39,131 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:23:39,131 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', 'wiki域', 'SaaS域', '平台功能', '证书域', '文件域', '实名域', '意愿域', '费用域']
2025-10-14 17:23:39,131 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:39,132 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:39,132 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:39,137 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:23:39,139 - __main__ - INFO - fee路由加载成功
2025-10-14 17:23:39,141 - __main__ - INFO - file路由加载成功
2025-10-14 17:23:39,143 - __main__ - INFO - identity路由加载成功
2025-10-14 17:23:39,147 - __main__ - INFO - intention路由加载成功
2025-10-14 17:23:39,158 - __main__ - INFO - platform路由加载成功
2025-10-14 17:23:39,166 - __main__ - INFO - saas路由加载成功
2025-10-14 17:23:39,175 - __main__ - INFO - signing路由加载成功
2025-10-14 17:23:39,177 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:23:39,179 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:39,179 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['签署域', 'wiki域', 'SaaS域', '平台功能', '证书域', '文件域', '实名域', '意愿域', '费用域']
2025-10-14 17:23:39,257 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:39,257 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:39,257 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:39,258 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:39,268 - werkzeug - WARNING -  * Debugger is active!
2025-10-14 17:23:39,271 - werkzeug - INFO -  * Debugger PIN: 453-144-825
2025-10-14 17:23:46,424 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:46,425 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:46,585 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:23:46,803 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:23:46,804 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', 'wiki域', '平台功能', 'SaaS域', '签署域', '意愿域', '文件域', '证书域', '实名域']
2025-10-14 17:23:46,804 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:46,804 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:46,804 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:46,811 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:23:46,813 - __main__ - INFO - fee路由加载成功
2025-10-14 17:23:46,814 - __main__ - INFO - file路由加载成功
2025-10-14 17:23:46,816 - __main__ - INFO - identity路由加载成功
2025-10-14 17:23:46,821 - __main__ - INFO - intention路由加载成功
2025-10-14 17:23:46,834 - __main__ - INFO - platform路由加载成功
2025-10-14 17:23:46,843 - __main__ - INFO - saas路由加载成功
2025-10-14 17:23:46,853 - __main__ - INFO - signing路由加载成功
2025-10-14 17:23:46,855 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:23:46,857 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:46,857 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['费用域', 'wiki域', '平台功能', 'SaaS域', '签署域', '意愿域', '文件域', '证书域', '实名域']
2025-10-14 17:23:46,945 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:46,945 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:46,945 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:46,945 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:46,945 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:23:46,946 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:23:46,946 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:46,946 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:23:46,946 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:23:49,652 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:49,653 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:49,781 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:23:49,974 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:23:49,975 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '平台功能', '证书域', '意愿域', 'SaaS域', '文件域', '实名域', '签署域', '费用域']
2025-10-14 17:23:49,975 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:49,975 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:49,975 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:49,981 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:23:49,983 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:23:49,984 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:23:49,986 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:23:49,991 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:23:50,002 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:23:50,010 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:23:50,018 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:23:50,020 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:23:50,022 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:50,022 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '平台功能', '证书域', '意愿域', 'SaaS域', '文件域', '实名域', '签署域', '费用域']
2025-10-14 17:23:50,106 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:50,106 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:50,106 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:50,106 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:50,186 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:23:50,186 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:23:50,187 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '平台功能', '证书域', '意愿域', 'SaaS域', '文件域', '实名域', '签署域', '费用域']
2025-10-14 17:23:50,187 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:23:50,187 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:23:50,187 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:23:50,194 - main - INFO - certificate路由加载成功
2025-10-14 17:23:50,195 - main - INFO - fee路由加载成功
2025-10-14 17:23:50,197 - main - INFO - file路由加载成功
2025-10-14 17:23:50,199 - main - INFO - identity路由加载成功
2025-10-14 17:23:50,205 - main - INFO - intention路由加载成功
2025-10-14 17:23:50,217 - main - INFO - platform路由加载成功
2025-10-14 17:23:50,226 - main - INFO - saas路由加载成功
2025-10-14 17:23:50,238 - main - INFO - signing路由加载成功
2025-10-14 17:23:50,241 - main - INFO - wiki路由加载成功
2025-10-14 17:23:50,242 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:23:50,242 - main - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '平台功能', '证书域', '意愿域', 'SaaS域', '文件域', '实名域', '签署域', '费用域']
2025-10-14 17:23:50,282 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:23:50,282 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:23:50,282 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:23:50,283 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:23:50,283 - main - INFO - 🚀 应用启动中...
2025-10-14 17:23:50,283 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:23:50,284 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:23:50,284 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:23:50,284 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:23:50,284 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:23:50,284 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:23:53,164 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:23:53,166 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:23:53,166 - main - INFO - ✅ 应用已关闭
2025-10-14 17:24:09,667 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:24:09,667 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:24:09,802 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:24:09,976 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:24:09,976 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '费用域', '签署域', '平台功能', 'SaaS域', 'wiki域', '实名域', '意愿域', '证书域']
2025-10-14 17:24:09,976 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:24:09,976 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:24:09,976 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:24:09,982 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:24:09,984 - __main__ - INFO - fee路由加载成功
2025-10-14 17:24:09,985 - __main__ - INFO - file路由加载成功
2025-10-14 17:24:09,987 - __main__ - INFO - identity路由加载成功
2025-10-14 17:24:09,993 - __main__ - INFO - intention路由加载成功
2025-10-14 17:24:10,003 - __main__ - INFO - platform路由加载成功
2025-10-14 17:24:10,011 - __main__ - INFO - saas路由加载成功
2025-10-14 17:24:10,019 - __main__ - INFO - signing路由加载成功
2025-10-14 17:24:10,022 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:24:10,024 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:24:10,024 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '费用域', '签署域', '平台功能', 'SaaS域', 'wiki域', '实名域', '意愿域', '证书域']
2025-10-14 17:24:10,104 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:24:10,104 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:24:10,104 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:24:10,104 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:24:10,104 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:24:10,104 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:24:10,104 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:24:10,104 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:24:10,105 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:24:12,824 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:24:12,824 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:24:12,957 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:24:13,147 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:24:13,148 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '文件域', '实名域', 'wiki域', '签署域', '平台功能', '意愿域', 'SaaS域', '证书域']
2025-10-14 17:24:13,148 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:24:13,148 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:24:13,148 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:24:13,154 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:24:13,157 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:24:13,157 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:24:13,159 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:24:13,163 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:24:13,175 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:24:13,183 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:24:13,192 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:24:13,194 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:24:13,196 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:24:13,196 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['费用域', '文件域', '实名域', 'wiki域', '签署域', '平台功能', '意愿域', 'SaaS域', '证书域']
2025-10-14 17:24:13,273 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:24:13,273 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:24:13,273 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:24:13,273 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:24:13,342 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:24:13,343 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:24:13,344 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '文件域', '实名域', 'wiki域', '签署域', '平台功能', '意愿域', 'SaaS域', '证书域']
2025-10-14 17:24:13,344 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:24:13,344 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:24:13,344 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:24:13,349 - main - INFO - certificate路由加载成功
2025-10-14 17:24:13,351 - main - INFO - fee路由加载成功
2025-10-14 17:24:13,353 - main - INFO - file路由加载成功
2025-10-14 17:24:13,356 - main - INFO - identity路由加载成功
2025-10-14 17:24:13,362 - main - INFO - intention路由加载成功
2025-10-14 17:24:13,375 - main - INFO - platform路由加载成功
2025-10-14 17:24:13,384 - main - INFO - saas路由加载成功
2025-10-14 17:24:13,396 - main - INFO - signing路由加载成功
2025-10-14 17:24:13,399 - main - INFO - wiki路由加载成功
2025-10-14 17:24:13,400 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:24:13,401 - main - INFO - 🏷️ 使用自动发现的标签: ['费用域', '文件域', '实名域', 'wiki域', '签署域', '平台功能', '意愿域', 'SaaS域', '证书域']
2025-10-14 17:24:13,440 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:24:13,440 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:24:13,440 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:24:13,440 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:24:13,441 - main - INFO - 🚀 应用启动中...
2025-10-14 17:24:13,441 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:24:13,442 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:24:13,442 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:24:13,442 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:24:13,442 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:24:13,442 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:25:16,166 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:25:16,168 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:25:16,168 - main - INFO - ✅ 应用已关闭
2025-10-14 17:25:16,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:25:16,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:25:16,431 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:25:21,518 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:25:21,519 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:25:22,001 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:25:22,751 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:25:22,752 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '签署域', '费用域', '证书域', '意愿域', 'SaaS域', '文件域', 'wiki域', '平台功能']
2025-10-14 17:25:22,752 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:25:22,752 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:25:22,753 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:25:22,784 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:25:22,791 - __main__ - INFO - fee路由加载成功
2025-10-14 17:25:22,794 - __main__ - INFO - file路由加载成功
2025-10-14 17:25:22,799 - __main__ - INFO - identity路由加载成功
2025-10-14 17:25:22,812 - __main__ - INFO - intention路由加载成功
2025-10-14 17:25:22,839 - __main__ - INFO - platform路由加载成功
2025-10-14 17:25:22,856 - __main__ - INFO - saas路由加载成功
2025-10-14 17:25:22,874 - __main__ - INFO - signing路由加载成功
2025-10-14 17:25:22,877 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:25:22,881 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:25:22,882 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', '签署域', '费用域', '证书域', '意愿域', 'SaaS域', '文件域', 'wiki域', '平台功能']
2025-10-14 17:25:23,017 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:25:23,017 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:25:23,017 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:25:23,017 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:25:23,018 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:25:23,018 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:25:23,018 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:25:23,018 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:25:23,018 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:25:26,020 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:25:26,021 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:25:26,173 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:25:26,415 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:25:26,415 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', 'wiki域', '费用域', '签署域', '文件域', '证书域', '意愿域', '实名域', 'SaaS域']
2025-10-14 17:25:26,416 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:25:26,416 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:25:26,416 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:25:26,422 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:25:26,424 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:25:26,426 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:25:26,427 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:25:26,432 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:25:26,445 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:25:26,454 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:25:26,468 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:25:26,470 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:25:26,472 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:25:26,473 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', 'wiki域', '费用域', '签署域', '文件域', '证书域', '意愿域', '实名域', 'SaaS域']
2025-10-14 17:25:26,569 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:25:26,569 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:25:26,569 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:25:26,569 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:25:26,647 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:25:26,648 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:25:26,648 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', 'wiki域', '费用域', '签署域', '文件域', '证书域', '意愿域', '实名域', 'SaaS域']
2025-10-14 17:25:26,648 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:25:26,648 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:25:26,648 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:25:26,657 - main - INFO - certificate路由加载成功
2025-10-14 17:25:26,661 - main - INFO - fee路由加载成功
2025-10-14 17:25:26,663 - main - INFO - file路由加载成功
2025-10-14 17:25:26,665 - main - INFO - identity路由加载成功
2025-10-14 17:25:26,672 - main - INFO - intention路由加载成功
2025-10-14 17:25:26,684 - main - INFO - platform路由加载成功
2025-10-14 17:25:26,694 - main - INFO - saas路由加载成功
2025-10-14 17:25:26,704 - main - INFO - signing路由加载成功
2025-10-14 17:25:26,706 - main - INFO - wiki路由加载成功
2025-10-14 17:25:26,708 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:25:26,708 - main - INFO - 🏷️ 使用自动发现的标签: ['平台功能', 'wiki域', '费用域', '签署域', '文件域', '证书域', '意愿域', '实名域', 'SaaS域']
2025-10-14 17:25:26,746 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:25:26,746 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:25:26,747 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:25:26,747 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:25:26,747 - main - INFO - 🚀 应用启动中...
2025-10-14 17:25:26,748 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:25:26,748 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:25:26,748 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:25:26,748 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:25:26,748 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:25:26,748 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:26:15,619 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:26:15,621 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:26:15,621 - main - INFO - ✅ 应用已关闭
2025-10-14 17:26:33,171 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:26:33,172 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:26:34,047 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:26:34,223 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:26:34,223 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', 'SaaS域', '签署域', '证书域', '费用域', '文件域', '实名域', 'wiki域', '平台功能']
2025-10-14 17:26:34,223 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:26:34,223 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:26:34,223 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:26:34,229 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:26:34,230 - __main__ - INFO - fee路由加载成功
2025-10-14 17:26:34,231 - __main__ - INFO - file路由加载成功
2025-10-14 17:26:34,233 - __main__ - INFO - identity路由加载成功
2025-10-14 17:26:34,238 - __main__ - INFO - intention路由加载成功
2025-10-14 17:26:34,248 - __main__ - INFO - platform路由加载成功
2025-10-14 17:26:34,256 - __main__ - INFO - saas路由加载成功
2025-10-14 17:26:34,265 - __main__ - INFO - signing路由加载成功
2025-10-14 17:26:34,266 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:26:34,269 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:26:34,269 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', 'SaaS域', '签署域', '证书域', '费用域', '文件域', '实名域', 'wiki域', '平台功能']
2025-10-14 17:26:34,303 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:26:34,304 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:26:34,304 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:26:34,304 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:26:34,304 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:26:34,304 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:26:34,304 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:26:34,304 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:26:34,304 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:26:36,105 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:26:36,106 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:26:37,070 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:26:37,256 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:26:37,256 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', 'wiki域', '签署域', '费用域', '实名域', 'SaaS域', '平台功能', '证书域']
2025-10-14 17:26:37,256 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:26:37,256 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:26:37,256 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:26:37,262 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:26:37,264 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:26:37,265 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:26:37,267 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:26:37,271 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:26:37,286 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:26:37,296 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:26:37,307 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:26:37,309 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:26:37,312 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:26:37,312 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', 'wiki域', '签署域', '费用域', '实名域', 'SaaS域', '平台功能', '证书域']
2025-10-14 17:26:37,353 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:26:37,353 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:26:37,353 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:26:37,353 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:26:37,428 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:26:37,428 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:26:37,428 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', 'wiki域', '签署域', '费用域', '实名域', 'SaaS域', '平台功能', '证书域']
2025-10-14 17:26:37,429 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:26:37,429 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:26:37,429 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:26:37,435 - main - INFO - certificate路由加载成功
2025-10-14 17:26:37,437 - main - INFO - fee路由加载成功
2025-10-14 17:26:37,438 - main - INFO - file路由加载成功
2025-10-14 17:26:37,440 - main - INFO - identity路由加载成功
2025-10-14 17:26:37,445 - main - INFO - intention路由加载成功
2025-10-14 17:26:37,464 - main - INFO - platform路由加载成功
2025-10-14 17:26:37,472 - main - INFO - saas路由加载成功
2025-10-14 17:26:37,483 - main - INFO - signing路由加载成功
2025-10-14 17:26:37,485 - main - INFO - wiki路由加载成功
2025-10-14 17:26:37,488 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:26:37,488 - main - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', 'wiki域', '签署域', '费用域', '实名域', 'SaaS域', '平台功能', '证书域']
2025-10-14 17:26:37,580 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:26:37,580 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:26:37,580 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:26:37,580 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:26:37,580 - main - INFO - 🚀 应用启动中...
2025-10-14 17:26:37,581 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:26:37,581 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:26:37,581 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:26:37,581 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:26:37,582 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:26:37,582 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:27:03,597 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:27:03,600 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:27:03,600 - main - INFO - ✅ 应用已关闭
2025-10-14 17:27:46,931 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:27:46,932 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:27:47,119 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:27:47,356 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:27:47,356 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '费用域', 'wiki域', '实名域', '意愿域', '签署域', '证书域', '平台功能', '文件域']
2025-10-14 17:27:47,356 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:27:47,357 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:27:47,357 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:27:47,364 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:27:47,367 - __main__ - INFO - fee路由加载成功
2025-10-14 17:27:47,369 - __main__ - INFO - file路由加载成功
2025-10-14 17:27:47,371 - __main__ - INFO - identity路由加载成功
2025-10-14 17:27:47,377 - __main__ - INFO - intention路由加载成功
2025-10-14 17:27:47,391 - __main__ - INFO - platform路由加载成功
2025-10-14 17:27:47,401 - __main__ - INFO - saas路由加载成功
2025-10-14 17:27:47,413 - __main__ - INFO - signing路由加载成功
2025-10-14 17:27:47,415 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:27:47,418 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:27:47,418 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '费用域', 'wiki域', '实名域', '意愿域', '签署域', '证书域', '平台功能', '文件域']
2025-10-14 17:27:47,508 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:27:47,508 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:27:47,508 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:27:47,508 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:27:47,508 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:27:47,508 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:27:47,508 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:27:47,508 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:27:47,508 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:27:50,571 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:27:50,574 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:27:50,762 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:27:50,985 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:27:50,985 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '费用域', '证书域', '文件域', '意愿域', '实名域', '签署域', 'SaaS域', '平台功能']
2025-10-14 17:27:50,985 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:27:50,985 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:27:50,986 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:27:50,993 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:27:50,995 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:27:50,996 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:27:50,998 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:27:51,003 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:27:51,015 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:27:51,022 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:27:51,033 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:27:51,035 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:27:51,038 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:27:51,038 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '费用域', '证书域', '文件域', '意愿域', '实名域', '签署域', 'SaaS域', '平台功能']
2025-10-14 17:27:51,127 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:27:51,127 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:27:51,127 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:27:51,127 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:27:51,202 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:27:51,203 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:27:51,203 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '费用域', '证书域', '文件域', '意愿域', '实名域', '签署域', 'SaaS域', '平台功能']
2025-10-14 17:27:51,204 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:27:51,204 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:27:51,204 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:27:51,212 - main - INFO - certificate路由加载成功
2025-10-14 17:27:51,214 - main - INFO - fee路由加载成功
2025-10-14 17:27:51,216 - main - INFO - file路由加载成功
2025-10-14 17:27:51,218 - main - INFO - identity路由加载成功
2025-10-14 17:27:51,224 - main - INFO - intention路由加载成功
2025-10-14 17:27:51,239 - main - INFO - platform路由加载成功
2025-10-14 17:27:51,250 - main - INFO - saas路由加载成功
2025-10-14 17:27:51,261 - main - INFO - signing路由加载成功
2025-10-14 17:27:51,264 - main - INFO - wiki路由加载成功
2025-10-14 17:27:51,265 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:27:51,266 - main - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '费用域', '证书域', '文件域', '意愿域', '实名域', '签署域', 'SaaS域', '平台功能']
2025-10-14 17:27:51,309 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:27:51,309 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:27:51,309 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:27:51,309 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:27:51,310 - main - INFO - 🚀 应用启动中...
2025-10-14 17:27:51,310 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:27:51,310 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:27:51,311 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:27:51,311 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:27:51,311 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:27:51,311 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:28:24,638 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:28:24,639 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:28:24,640 - main - INFO - ✅ 应用已关闭
2025-10-14 17:28:24,917 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:28:24,918 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:28:24,920 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:28:28,013 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:28,014 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:28,193 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:28:28,370 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:28:28,371 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '签署域', '文件域', '费用域', '意愿域', '平台功能', 'wiki域', '证书域', '实名域']
2025-10-14 17:28:28,371 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:28,371 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:28,371 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:28,376 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:28:28,378 - __main__ - INFO - fee路由加载成功
2025-10-14 17:28:28,379 - __main__ - INFO - file路由加载成功
2025-10-14 17:28:28,383 - __main__ - INFO - identity路由加载成功
2025-10-14 17:28:28,387 - __main__ - INFO - intention路由加载成功
2025-10-14 17:28:28,401 - __main__ - INFO - platform路由加载成功
2025-10-14 17:28:28,408 - __main__ - INFO - saas路由加载成功
2025-10-14 17:28:28,418 - __main__ - INFO - signing路由加载成功
2025-10-14 17:28:28,420 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:28:28,421 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:28,422 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '签署域', '文件域', '费用域', '意愿域', '平台功能', 'wiki域', '证书域', '实名域']
2025-10-14 17:28:28,506 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:28,506 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:28,506 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:28,506 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:28,506 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:28:28,506 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:28:28,506 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:28,507 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:28:28,507 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:28:32,694 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:32,694 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:32,882 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:28:33,182 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:28:33,183 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '文件域', '意愿域', 'wiki域', '证书域', '实名域', '平台功能', '费用域', '签署域']
2025-10-14 17:28:33,183 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:33,183 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:33,183 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:33,190 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:28:33,193 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:28:33,194 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:28:33,197 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:28:33,202 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:28:33,216 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:28:33,226 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:28:33,239 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:28:33,241 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:28:33,244 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:33,244 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '文件域', '意愿域', 'wiki域', '证书域', '实名域', '平台功能', '费用域', '签署域']
2025-10-14 17:28:33,496 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:33,496 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:33,496 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:33,496 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:33,597 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:33,598 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:33,598 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '文件域', '意愿域', 'wiki域', '证书域', '实名域', '平台功能', '费用域', '签署域']
2025-10-14 17:28:33,599 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:33,599 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:33,599 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:33,615 - main - INFO - certificate路由加载成功
2025-10-14 17:28:33,620 - main - INFO - fee路由加载成功
2025-10-14 17:28:33,622 - main - INFO - file路由加载成功
2025-10-14 17:28:33,624 - main - INFO - identity路由加载成功
2025-10-14 17:28:33,632 - main - INFO - intention路由加载成功
2025-10-14 17:28:33,650 - main - INFO - platform路由加载成功
2025-10-14 17:28:33,663 - main - INFO - saas路由加载成功
2025-10-14 17:28:33,676 - main - INFO - signing路由加载成功
2025-10-14 17:28:33,678 - main - INFO - wiki路由加载成功
2025-10-14 17:28:33,680 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:33,681 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '文件域', '意愿域', 'wiki域', '证书域', '实名域', '平台功能', '费用域', '签署域']
2025-10-14 17:28:33,728 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:33,729 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:33,729 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:33,729 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:33,729 - main - INFO - 🚀 应用启动中...
2025-10-14 17:28:33,730 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:28:33,730 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:28:33,730 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:28:33,731 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:28:33,731 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:28:33,731 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:28:41,652 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:41,653 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:41,835 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:28:42,072 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:28:42,072 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '签署域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '证书域', '文件域', '费用域']
2025-10-14 17:28:42,072 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:42,073 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:42,073 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:42,080 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:28:42,082 - __main__ - INFO - fee路由加载成功
2025-10-14 17:28:42,084 - __main__ - INFO - file路由加载成功
2025-10-14 17:28:42,086 - __main__ - INFO - identity路由加载成功
2025-10-14 17:28:42,091 - __main__ - INFO - intention路由加载成功
2025-10-14 17:28:42,105 - __main__ - INFO - platform路由加载成功
2025-10-14 17:28:42,114 - __main__ - INFO - saas路由加载成功
2025-10-14 17:28:42,125 - __main__ - INFO - signing路由加载成功
2025-10-14 17:28:42,127 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:28:42,130 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:42,130 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', '签署域', '平台功能', '意愿域', 'wiki域', 'SaaS域', '证书域', '文件域', '费用域']
2025-10-14 17:28:42,217 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:42,218 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:42,218 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:42,218 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:42,218 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:28:42,218 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:28:42,218 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:42,218 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:28:42,218 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:28:45,234 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:45,234 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:45,381 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:28:45,576 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:28:45,577 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '文件域', 'wiki域', '平台功能', '实名域', '费用域', '意愿域', 'SaaS域', '签署域']
2025-10-14 17:28:45,577 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:45,577 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:45,577 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:45,583 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:28:45,585 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:28:45,586 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:28:45,587 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:28:45,592 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:28:45,604 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:28:45,612 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:28:45,620 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:28:45,622 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:28:45,624 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:45,625 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '文件域', 'wiki域', '平台功能', '实名域', '费用域', '意愿域', 'SaaS域', '签署域']
2025-10-14 17:28:45,703 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:45,703 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:45,703 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:45,703 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:45,786 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:28:45,787 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:28:45,787 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '文件域', 'wiki域', '平台功能', '实名域', '费用域', '意愿域', 'SaaS域', '签署域']
2025-10-14 17:28:45,787 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:28:45,787 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:28:45,787 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:28:45,794 - main - INFO - certificate路由加载成功
2025-10-14 17:28:45,796 - main - INFO - fee路由加载成功
2025-10-14 17:28:45,799 - main - INFO - file路由加载成功
2025-10-14 17:28:45,802 - main - INFO - identity路由加载成功
2025-10-14 17:28:45,807 - main - INFO - intention路由加载成功
2025-10-14 17:28:45,822 - main - INFO - platform路由加载成功
2025-10-14 17:28:45,831 - main - INFO - saas路由加载成功
2025-10-14 17:28:45,842 - main - INFO - signing路由加载成功
2025-10-14 17:28:45,845 - main - INFO - wiki路由加载成功
2025-10-14 17:28:45,848 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:28:45,848 - main - INFO - 🏷️ 使用自动发现的标签: ['证书域', '文件域', 'wiki域', '平台功能', '实名域', '费用域', '意愿域', 'SaaS域', '签署域']
2025-10-14 17:28:45,891 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:28:45,891 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:28:45,891 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:28:45,891 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:28:45,892 - main - INFO - 🚀 应用启动中...
2025-10-14 17:28:45,892 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:28:45,893 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:28:45,893 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:28:45,893 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:28:45,893 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:28:45,893 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:29:35,585 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:29:35,587 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:29:35,587 - main - INFO - ✅ 应用已关闭
2025-10-14 17:29:35,892 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://**************:5001
2025-10-14 17:29:35,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:29:35,896 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:30:30,191 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:30:30,191 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:30:30,415 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:30:30,584 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:30:30,584 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '费用域', '实名域', 'wiki域', '签署域', '平台功能', '证书域', 'SaaS域', '文件域']
2025-10-14 17:30:30,584 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:30:30,584 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:30:30,584 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:30:30,592 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:30:30,595 - __main__ - INFO - fee路由加载成功
2025-10-14 17:30:30,597 - __main__ - INFO - file路由加载成功
2025-10-14 17:30:30,599 - __main__ - INFO - identity路由加载成功
2025-10-14 17:30:30,604 - __main__ - INFO - intention路由加载成功
2025-10-14 17:30:30,614 - __main__ - INFO - platform路由加载成功
2025-10-14 17:30:30,622 - __main__ - INFO - saas路由加载成功
2025-10-14 17:30:30,632 - __main__ - INFO - signing路由加载成功
2025-10-14 17:30:30,634 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:30:30,637 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:30:30,637 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '费用域', '实名域', 'wiki域', '签署域', '平台功能', '证书域', 'SaaS域', '文件域']
2025-10-14 17:30:30,716 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:30:30,716 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:30:30,716 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:30:30,716 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:30,731 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:30:30,731 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:30:30,734 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:30:33,556 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:30:33,556 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:30:33,825 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:30:34,037 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:30:34,037 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', 'SaaS域', '费用域', '意愿域', '实名域', '平台功能', '文件域', '签署域', '证书域']
2025-10-14 17:30:34,037 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:30:34,037 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:30:34,037 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:30:34,047 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:30:34,049 - __main__ - INFO - fee路由加载成功
2025-10-14 17:30:34,050 - __main__ - INFO - file路由加载成功
2025-10-14 17:30:34,052 - __main__ - INFO - identity路由加载成功
2025-10-14 17:30:34,058 - __main__ - INFO - intention路由加载成功
2025-10-14 17:30:34,074 - __main__ - INFO - platform路由加载成功
2025-10-14 17:30:34,085 - __main__ - INFO - saas路由加载成功
2025-10-14 17:30:34,101 - __main__ - INFO - signing路由加载成功
2025-10-14 17:30:34,104 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:30:34,108 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:30:34,108 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', 'SaaS域', '费用域', '意愿域', '实名域', '平台功能', '文件域', '签署域', '证书域']
2025-10-14 17:30:34,203 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:30:34,203 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:30:34,203 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:30:34,203 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:34,215 - werkzeug - WARNING -  * Debugger is active!
2025-10-14 17:30:34,218 - werkzeug - INFO -  * Debugger PIN: 453-144-825
2025-10-14 17:30:38,537 - werkzeug - INFO - 127.0.0.1 - - [14/Oct/2025 17:30:38] "GET / HTTP/1.1" 200 -
2025-10-14 17:30:40,629 - werkzeug - INFO - 127.0.0.1 - - [14/Oct/2025 17:30:40] "GET /api/discover-tools HTTP/1.1" 200 -
2025-10-14 17:30:43,294 - werkzeug - INFO - 127.0.0.1 - - [14/Oct/2025 17:30:43] "GET /api/discover-tools HTTP/1.1" 200 -
2025-10-14 17:30:43,666 - werkzeug - INFO - 127.0.0.1 - - [14/Oct/2025 17:30:43] "GET /api/server-status HTTP/1.1" 200 -
2025-10-14 17:30:46,304 - werkzeug - INFO - 127.0.0.1 - - [14/Oct/2025 17:30:46] "GET /api/server-status HTTP/1.1" 200 -
2025-10-14 17:30:52,147 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:30:52,148 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:30:52,315 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:30:52,500 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:30:52,500 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', '平台功能', 'SaaS域', 'wiki域', '证书域', '意愿域', '实名域', '文件域', '费用域']
2025-10-14 17:30:52,500 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:30:52,500 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:30:52,500 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:30:52,506 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:30:52,508 - __main__ - INFO - fee路由加载成功
2025-10-14 17:30:52,510 - __main__ - INFO - file路由加载成功
2025-10-14 17:30:52,512 - __main__ - INFO - identity路由加载成功
2025-10-14 17:30:52,516 - __main__ - INFO - intention路由加载成功
2025-10-14 17:30:52,527 - __main__ - INFO - platform路由加载成功
2025-10-14 17:30:52,536 - __main__ - INFO - saas路由加载成功
2025-10-14 17:30:52,544 - __main__ - INFO - signing路由加载成功
2025-10-14 17:30:52,546 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:30:52,549 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:30:52,549 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['签署域', '平台功能', 'SaaS域', 'wiki域', '证书域', '意愿域', '实名域', '文件域', '费用域']
2025-10-14 17:30:52,628 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:30:52,628 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:30:52,628 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:30:52,628 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:52,628 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:30:52,628 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:30:52,629 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:52,629 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:30:52,629 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:30:55,316 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:30:55,317 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:30:55,451 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:30:55,658 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:30:55,659 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '签署域', '实名域', '意愿域', '文件域', '证书域', '平台功能', '费用域', 'wiki域']
2025-10-14 17:30:55,659 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:30:55,659 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:30:55,659 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:30:55,665 - __mp_main__ - INFO - certificate路由加载成功
2025-10-14 17:30:55,667 - __mp_main__ - INFO - fee路由加载成功
2025-10-14 17:30:55,668 - __mp_main__ - INFO - file路由加载成功
2025-10-14 17:30:55,670 - __mp_main__ - INFO - identity路由加载成功
2025-10-14 17:30:55,675 - __mp_main__ - INFO - intention路由加载成功
2025-10-14 17:30:55,687 - __mp_main__ - INFO - platform路由加载成功
2025-10-14 17:30:55,695 - __mp_main__ - INFO - saas路由加载成功
2025-10-14 17:30:55,705 - __mp_main__ - INFO - signing路由加载成功
2025-10-14 17:30:55,707 - __mp_main__ - INFO - wiki路由加载成功
2025-10-14 17:30:55,709 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:30:55,710 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '签署域', '实名域', '意愿域', '文件域', '证书域', '平台功能', '费用域', 'wiki域']
2025-10-14 17:30:55,792 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:30:55,792 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:30:55,792 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:30:55,792 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:55,867 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:30:55,867 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:30:55,868 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '签署域', '实名域', '意愿域', '文件域', '证书域', '平台功能', '费用域', 'wiki域']
2025-10-14 17:30:55,868 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:30:55,868 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:30:55,868 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:30:55,876 - main - INFO - certificate路由加载成功
2025-10-14 17:30:55,878 - main - INFO - fee路由加载成功
2025-10-14 17:30:55,880 - main - INFO - file路由加载成功
2025-10-14 17:30:55,882 - main - INFO - identity路由加载成功
2025-10-14 17:30:55,888 - main - INFO - intention路由加载成功
2025-10-14 17:30:55,903 - main - INFO - platform路由加载成功
2025-10-14 17:30:55,913 - main - INFO - saas路由加载成功
2025-10-14 17:30:55,925 - main - INFO - signing路由加载成功
2025-10-14 17:30:55,928 - main - INFO - wiki路由加载成功
2025-10-14 17:30:55,930 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:30:55,930 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '签署域', '实名域', '意愿域', '文件域', '证书域', '平台功能', '费用域', 'wiki域']
2025-10-14 17:30:55,970 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:30:55,970 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:30:55,970 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:30:55,970 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:30:55,971 - main - INFO - 🚀 应用启动中...
2025-10-14 17:30:55,971 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:30:55,972 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:30:55,972 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:30:55,972 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:30:55,972 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:30:55,972 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:31:20,583 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:31:20,584 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:31:20,585 - main - INFO - ✅ 应用已关闭
2025-10-14 17:31:27,250 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:31:27,251 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:31:27,397 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:31:27,630 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:31:27,630 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '签署域', '意愿域', '费用域', '文件域', '实名域', '平台功能', '证书域', 'wiki域']
2025-10-14 17:31:27,631 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:31:27,631 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:31:27,631 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:31:27,637 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:31:27,640 - __main__ - INFO - fee路由加载成功
2025-10-14 17:31:27,641 - __main__ - INFO - file路由加载成功
2025-10-14 17:31:27,643 - __main__ - INFO - identity路由加载成功
2025-10-14 17:31:27,648 - __main__ - INFO - intention路由加载成功
2025-10-14 17:31:27,660 - __main__ - INFO - platform路由加载成功
2025-10-14 17:31:27,668 - __main__ - INFO - saas路由加载成功
2025-10-14 17:31:27,680 - __main__ - INFO - signing路由加载成功
2025-10-14 17:31:27,682 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:31:27,685 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:31:27,685 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '签署域', '意愿域', '费用域', '文件域', '实名域', '平台功能', '证书域', 'wiki域']
2025-10-14 17:31:27,771 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:31:27,771 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:31:27,771 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:31:27,772 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:27,772 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:31:27,772 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:31:27,772 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:27,772 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:31:27,772 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:31:27,836 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:31:27,838 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:31:27,838 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '签署域', '意愿域', '费用域', '文件域', '实名域', '平台功能', '证书域', 'wiki域']
2025-10-14 17:31:27,838 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:31:27,838 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:31:27,838 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:31:27,846 - main - INFO - certificate路由加载成功
2025-10-14 17:31:27,848 - main - INFO - fee路由加载成功
2025-10-14 17:31:27,849 - main - INFO - file路由加载成功
2025-10-14 17:31:27,851 - main - INFO - identity路由加载成功
2025-10-14 17:31:27,860 - main - INFO - intention路由加载成功
2025-10-14 17:31:27,877 - main - INFO - platform路由加载成功
2025-10-14 17:31:27,893 - main - INFO - saas路由加载成功
2025-10-14 17:31:27,907 - main - INFO - signing路由加载成功
2025-10-14 17:31:27,910 - main - INFO - wiki路由加载成功
2025-10-14 17:31:27,912 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:31:27,912 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '签署域', '意愿域', '费用域', '文件域', '实名域', '平台功能', '证书域', 'wiki域']
2025-10-14 17:31:27,961 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:31:27,963 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:31:27,963 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:31:27,963 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:27,963 - main - INFO - 🚀 应用启动中...
2025-10-14 17:31:27,964 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:31:27,964 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:31:27,964 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:31:27,964 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:31:27,964 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:31:27,964 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:31:48,348 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:31:48,350 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:31:48,350 - main - INFO - ✅ 应用已关闭
2025-10-14 17:31:48,367 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:31:48,367 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:31:48,369 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:31:50,887 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:31:50,887 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:31:51,046 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:31:51,226 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:31:51,226 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', '签署域', '费用域', '文件域', '意愿域', '平台功能', 'SaaS域', '证书域']
2025-10-14 17:31:51,227 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:31:51,227 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:31:51,227 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:31:51,233 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:31:51,235 - __main__ - INFO - fee路由加载成功
2025-10-14 17:31:51,236 - __main__ - INFO - file路由加载成功
2025-10-14 17:31:51,238 - __main__ - INFO - identity路由加载成功
2025-10-14 17:31:51,243 - __main__ - INFO - intention路由加载成功
2025-10-14 17:31:51,253 - __main__ - INFO - platform路由加载成功
2025-10-14 17:31:51,260 - __main__ - INFO - saas路由加载成功
2025-10-14 17:31:51,269 - __main__ - INFO - signing路由加载成功
2025-10-14 17:31:51,271 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:31:51,273 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:31:51,273 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', '签署域', '费用域', '文件域', '意愿域', '平台功能', 'SaaS域', '证书域']
2025-10-14 17:31:51,356 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:31:51,356 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:31:51,356 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:31:51,356 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:51,356 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:31:51,356 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:31:51,356 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:51,356 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:31:51,357 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:31:51,422 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:31:51,422 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:31:51,422 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', '签署域', '费用域', '文件域', '意愿域', '平台功能', 'SaaS域', '证书域']
2025-10-14 17:31:51,423 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:31:51,423 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:31:51,423 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:31:51,430 - main - INFO - certificate路由加载成功
2025-10-14 17:31:51,432 - main - INFO - fee路由加载成功
2025-10-14 17:31:51,433 - main - INFO - file路由加载成功
2025-10-14 17:31:51,436 - main - INFO - identity路由加载成功
2025-10-14 17:31:51,441 - main - INFO - intention路由加载成功
2025-10-14 17:31:51,455 - main - INFO - platform路由加载成功
2025-10-14 17:31:51,462 - main - INFO - saas路由加载成功
2025-10-14 17:31:51,476 - main - INFO - signing路由加载成功
2025-10-14 17:31:51,478 - main - INFO - wiki路由加载成功
2025-10-14 17:31:51,480 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:31:51,480 - main - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', '签署域', '费用域', '文件域', '意愿域', '平台功能', 'SaaS域', '证书域']
2025-10-14 17:31:51,526 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:31:51,526 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:31:51,527 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:31:51,527 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:31:51,527 - main - INFO - 🚀 应用启动中...
2025-10-14 17:31:51,527 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:31:51,529 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:31:51,529 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:31:51,529 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:31:51,529 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:31:51,529 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:32:00,909 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:32:00,911 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:32:01,226 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:32:01,949 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:32:01,949 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '平台功能', '签署域', 'SaaS域', '意愿域', '文件域', '证书域', '实名域', 'wiki域']
2025-10-14 17:32:01,950 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:32:01,950 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:32:01,950 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:32:01,970 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:32:01,976 - __main__ - INFO - fee路由加载成功
2025-10-14 17:32:01,979 - __main__ - INFO - file路由加载成功
2025-10-14 17:32:01,987 - __main__ - INFO - identity路由加载成功
2025-10-14 17:32:02,001 - __main__ - INFO - intention路由加载成功
2025-10-14 17:32:02,035 - __main__ - INFO - platform路由加载成功
2025-10-14 17:32:02,063 - __main__ - INFO - saas路由加载成功
2025-10-14 17:32:02,096 - __main__ - INFO - signing路由加载成功
2025-10-14 17:32:02,101 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:32:02,107 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:32:02,108 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['费用域', '平台功能', '签署域', 'SaaS域', '意愿域', '文件域', '证书域', '实名域', 'wiki域']
2025-10-14 17:32:02,251 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:32:02,251 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:32:02,251 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:32:02,251 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:32:06,369 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:32:06,369 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:32:06,369 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:32:06,370 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:32:06,370 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:32:06,554 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:32:06,555 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:32:06,556 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '平台功能', '签署域', 'SaaS域', '意愿域', '文件域', '证书域', '实名域', 'wiki域']
2025-10-14 17:32:06,556 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:32:06,556 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:32:06,557 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:32:06,585 - main - INFO - certificate路由加载成功
2025-10-14 17:32:06,594 - main - INFO - fee路由加载成功
2025-10-14 17:32:06,599 - main - INFO - file路由加载成功
2025-10-14 17:32:06,608 - main - INFO - identity路由加载成功
2025-10-14 17:32:06,632 - main - INFO - intention路由加载成功
2025-10-14 17:32:06,684 - main - INFO - platform路由加载成功
2025-10-14 17:32:06,719 - main - INFO - saas路由加载成功
2025-10-14 17:32:06,761 - main - INFO - signing路由加载成功
2025-10-14 17:32:06,766 - main - INFO - wiki路由加载成功
2025-10-14 17:32:06,771 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:32:06,772 - main - INFO - 🏷️ 使用自动发现的标签: ['费用域', '平台功能', '签署域', 'SaaS域', '意愿域', '文件域', '证书域', '实名域', 'wiki域']
2025-10-14 17:32:06,950 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:32:06,951 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:32:06,951 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:32:06,951 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:32:06,954 - main - INFO - 🚀 应用启动中...
2025-10-14 17:32:06,954 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:32:06,955 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:32:06,955 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:32:06,956 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:32:06,956 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:32:06,956 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:32:38,094 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:32:38,096 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:32:38,096 - main - INFO - ✅ 应用已关闭
2025-10-14 17:32:55,818 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:32:55,820 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:32:56,045 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:32:57,092 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:32:57,093 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '证书域', '平台功能', '签署域', '费用域', 'wiki域', '文件域', 'SaaS域', '实名域']
2025-10-14 17:32:57,093 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:32:57,093 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:32:57,094 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:32:57,116 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:32:57,122 - __main__ - INFO - fee路由加载成功
2025-10-14 17:32:57,125 - __main__ - INFO - file路由加载成功
2025-10-14 17:32:57,132 - __main__ - INFO - identity路由加载成功
2025-10-14 17:32:57,147 - __main__ - INFO - intention路由加载成功
2025-10-14 17:32:57,185 - __main__ - INFO - platform路由加载成功
2025-10-14 17:32:57,214 - __main__ - INFO - saas路由加载成功
2025-10-14 17:32:57,246 - __main__ - INFO - signing路由加载成功
2025-10-14 17:32:57,252 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:32:57,260 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:32:57,260 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '证书域', '平台功能', '签署域', '费用域', 'wiki域', '文件域', 'SaaS域', '实名域']
2025-10-14 17:32:57,415 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:32:57,415 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:32:57,415 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:32:57,415 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:33:00,520 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-10-14 17:33:00,520 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-10-14 17:33:00,530 - werkzeug - INFO -  * Restarting with stat
2025-10-14 17:48:27,759 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:48:27,760 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:48:27,907 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:48:28,084 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:48:28,084 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '证书域', '意愿域', '实名域', '费用域', '签署域', 'SaaS域', '平台功能', 'wiki域']
2025-10-14 17:48:28,084 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:48:28,085 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:48:28,085 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:48:28,090 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:48:28,092 - __main__ - INFO - fee路由加载成功
2025-10-14 17:48:28,093 - __main__ - INFO - file路由加载成功
2025-10-14 17:48:28,094 - __main__ - INFO - identity路由加载成功
2025-10-14 17:48:28,100 - __main__ - INFO - intention路由加载成功
2025-10-14 17:48:28,112 - __main__ - INFO - platform路由加载成功
2025-10-14 17:48:28,121 - __main__ - INFO - saas路由加载成功
2025-10-14 17:48:28,130 - __main__ - INFO - signing路由加载成功
2025-10-14 17:48:28,133 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:48:28,135 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:48:28,135 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '证书域', '意愿域', '实名域', '费用域', '签署域', 'SaaS域', '平台功能', 'wiki域']
2025-10-14 17:48:28,219 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:48:28,219 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:48:28,219 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:48:28,219 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:48:28,220 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:48:28,220 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:48:28,220 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:48:28,220 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:48:28,221 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:48:28,286 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:48:28,287 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:48:28,287 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '证书域', '意愿域', '实名域', '费用域', '签署域', 'SaaS域', '平台功能', 'wiki域']
2025-10-14 17:48:28,287 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:48:28,287 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:48:28,288 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:48:28,294 - main - INFO - certificate路由加载成功
2025-10-14 17:48:28,296 - main - INFO - fee路由加载成功
2025-10-14 17:48:28,297 - main - INFO - file路由加载成功
2025-10-14 17:48:28,299 - main - INFO - identity路由加载成功
2025-10-14 17:48:28,305 - main - INFO - intention路由加载成功
2025-10-14 17:48:28,320 - main - INFO - platform路由加载成功
2025-10-14 17:48:28,333 - main - INFO - saas路由加载成功
2025-10-14 17:48:28,342 - main - INFO - signing路由加载成功
2025-10-14 17:48:28,345 - main - INFO - wiki路由加载成功
2025-10-14 17:48:28,346 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:48:28,346 - main - INFO - 🏷️ 使用自动发现的标签: ['文件域', '证书域', '意愿域', '实名域', '费用域', '签署域', 'SaaS域', '平台功能', 'wiki域']
2025-10-14 17:48:28,393 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:48:28,393 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:48:28,393 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:48:28,393 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:48:28,394 - main - INFO - 🚀 应用启动中...
2025-10-14 17:48:28,394 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:48:28,395 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:48:28,395 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:48:28,395 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:48:28,395 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:48:28,395 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:48:32,870 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:48:32,872 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:48:32,872 - main - INFO - ✅ 应用已关闭
2025-10-14 17:52:33,847 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:52:33,848 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:52:34,106 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:52:34,282 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:52:34,282 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '实名域', '意愿域', '平台功能', 'SaaS域', '证书域', '签署域', '文件域', '费用域']
2025-10-14 17:52:34,282 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:52:34,282 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:52:34,282 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:52:34,288 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:52:34,289 - __main__ - INFO - fee路由加载成功
2025-10-14 17:52:34,290 - __main__ - INFO - file路由加载成功
2025-10-14 17:52:34,292 - __main__ - INFO - identity路由加载成功
2025-10-14 17:52:34,296 - __main__ - INFO - intention路由加载成功
2025-10-14 17:52:34,306 - __main__ - INFO - platform路由加载成功
2025-10-14 17:52:34,316 - __main__ - INFO - saas路由加载成功
2025-10-14 17:52:34,325 - __main__ - INFO - signing路由加载成功
2025-10-14 17:52:34,328 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:52:34,331 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:52:34,331 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '实名域', '意愿域', '平台功能', 'SaaS域', '证书域', '签署域', '文件域', '费用域']
2025-10-14 17:52:34,416 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:52:34,416 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:52:34,416 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:52:34,416 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:52:34,416 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:52:34,417 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:52:34,417 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:52:34,418 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:52:34,418 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:52:34,480 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:52:34,480 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:52:34,480 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['wiki域', '实名域', '意愿域', '平台功能', 'SaaS域', '证书域', '签署域', '文件域', '费用域']
2025-10-14 17:52:34,480 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:52:34,481 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:52:34,481 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:52:34,486 - main - INFO - certificate路由加载成功
2025-10-14 17:52:34,488 - main - INFO - fee路由加载成功
2025-10-14 17:52:34,489 - main - INFO - file路由加载成功
2025-10-14 17:52:34,491 - main - INFO - identity路由加载成功
2025-10-14 17:52:34,495 - main - INFO - intention路由加载成功
2025-10-14 17:52:34,506 - main - INFO - platform路由加载成功
2025-10-14 17:52:34,513 - main - INFO - saas路由加载成功
2025-10-14 17:52:34,523 - main - INFO - signing路由加载成功
2025-10-14 17:52:34,525 - main - INFO - wiki路由加载成功
2025-10-14 17:52:34,528 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:52:34,528 - main - INFO - 🏷️ 使用自动发现的标签: ['wiki域', '实名域', '意愿域', '平台功能', 'SaaS域', '证书域', '签署域', '文件域', '费用域']
2025-10-14 17:52:34,571 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:52:34,571 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:52:34,571 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:52:34,571 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:52:34,572 - main - INFO - 🚀 应用启动中...
2025-10-14 17:52:34,572 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:52:34,572 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:52:34,572 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:52:34,572 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:52:34,572 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:52:34,573 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:52:53,832 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:52:53,835 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:52:53,835 - main - INFO - ✅ 应用已关闭
2025-10-14 17:53:04,098 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:53:04,099 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:53:04,235 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:53:04,450 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:53:04,450 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '意愿域', '证书域', '文件域', '签署域', '费用域', '实名域', '平台功能', 'wiki域']
2025-10-14 17:53:04,450 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:53:04,451 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:53:04,451 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:53:04,456 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:53:04,458 - __main__ - INFO - fee路由加载成功
2025-10-14 17:53:04,459 - __main__ - INFO - file路由加载成功
2025-10-14 17:53:04,461 - __main__ - INFO - identity路由加载成功
2025-10-14 17:53:04,465 - __main__ - INFO - intention路由加载成功
2025-10-14 17:53:04,475 - __main__ - INFO - platform路由加载成功
2025-10-14 17:53:04,483 - __main__ - INFO - saas路由加载成功
2025-10-14 17:53:04,493 - __main__ - INFO - signing路由加载成功
2025-10-14 17:53:04,494 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:53:04,498 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:53:04,498 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '意愿域', '证书域', '文件域', '签署域', '费用域', '实名域', '平台功能', 'wiki域']
2025-10-14 17:53:04,579 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:53:04,580 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:53:04,580 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:53:04,580 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:53:04,580 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:53:04,580 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:53:04,580 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:53:04,580 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:53:04,580 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:53:04,634 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:53:04,635 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:53:04,635 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '意愿域', '证书域', '文件域', '签署域', '费用域', '实名域', '平台功能', 'wiki域']
2025-10-14 17:53:04,635 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:53:04,635 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:53:04,635 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:53:04,641 - main - INFO - certificate路由加载成功
2025-10-14 17:53:04,643 - main - INFO - fee路由加载成功
2025-10-14 17:53:04,644 - main - INFO - file路由加载成功
2025-10-14 17:53:04,646 - main - INFO - identity路由加载成功
2025-10-14 17:53:04,650 - main - INFO - intention路由加载成功
2025-10-14 17:53:04,663 - main - INFO - platform路由加载成功
2025-10-14 17:53:04,672 - main - INFO - saas路由加载成功
2025-10-14 17:53:04,683 - main - INFO - signing路由加载成功
2025-10-14 17:53:04,686 - main - INFO - wiki路由加载成功
2025-10-14 17:53:04,688 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:53:04,688 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '意愿域', '证书域', '文件域', '签署域', '费用域', '实名域', '平台功能', 'wiki域']
2025-10-14 17:53:04,727 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:53:04,727 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:53:04,727 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:53:04,727 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:53:04,727 - main - INFO - 🚀 应用启动中...
2025-10-14 17:53:04,728 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:53:04,728 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:53:04,728 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:53:04,728 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:53:04,728 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:53:04,728 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 17:53:48,080 - main - INFO - 🛑 应用关闭中...
2025-10-14 17:53:48,083 - main - INFO - ✅ 连接状态已保存
2025-10-14 17:53:48,083 - main - INFO - ✅ 应用已关闭
2025-10-14 17:54:17,090 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:54:17,091 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:54:18,045 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 17:54:18,250 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 17:54:18,251 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', '费用域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '证书域']
2025-10-14 17:54:18,251 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:54:18,251 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:54:18,251 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:54:18,258 - __main__ - INFO - certificate路由加载成功
2025-10-14 17:54:18,262 - __main__ - INFO - fee路由加载成功
2025-10-14 17:54:18,264 - __main__ - INFO - file路由加载成功
2025-10-14 17:54:18,267 - __main__ - INFO - identity路由加载成功
2025-10-14 17:54:18,271 - __main__ - INFO - intention路由加载成功
2025-10-14 17:54:18,284 - __main__ - INFO - platform路由加载成功
2025-10-14 17:54:18,295 - __main__ - INFO - saas路由加载成功
2025-10-14 17:54:18,310 - __main__ - INFO - signing路由加载成功
2025-10-14 17:54:18,313 - __main__ - INFO - wiki路由加载成功
2025-10-14 17:54:18,316 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 17:54:18,316 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', '费用域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '证书域']
2025-10-14 17:54:18,357 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:54:18,357 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:54:18,357 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:54:18,357 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:54:18,357 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 17:54:18,357 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 17:54:18,357 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:54:18,357 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 17:54:18,357 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 17:54:18,417 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 17:54:18,418 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 17:54:18,418 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['文件域', '意愿域', '费用域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '证书域']
2025-10-14 17:54:18,418 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 17:54:18,418 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 17:54:18,419 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 17:54:18,425 - main - INFO - certificate路由加载成功
2025-10-14 17:54:18,426 - main - INFO - fee路由加载成功
2025-10-14 17:54:18,427 - main - INFO - file路由加载成功
2025-10-14 17:54:18,429 - main - INFO - identity路由加载成功
2025-10-14 17:54:18,433 - main - INFO - intention路由加载成功
2025-10-14 17:54:18,444 - main - INFO - platform路由加载成功
2025-10-14 17:54:18,452 - main - INFO - saas路由加载成功
2025-10-14 17:54:18,462 - main - INFO - signing路由加载成功
2025-10-14 17:54:18,465 - main - INFO - wiki路由加载成功
2025-10-14 17:54:18,467 - main - INFO - 🚀 设置MCP服务...
2025-10-14 17:54:18,468 - main - INFO - 🏷️ 使用自动发现的标签: ['文件域', '意愿域', '费用域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '证书域']
2025-10-14 17:54:18,570 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 17:54:18,570 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 17:54:18,570 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 17:54:18,570 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 17:54:18,571 - main - INFO - 🚀 应用启动中...
2025-10-14 17:54:18,571 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 17:54:18,572 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 17:54:18,572 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 17:54:18,572 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 17:54:18,572 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 17:54:18,572 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 18:02:10,579 - main - INFO - 🛑 应用关闭中...
2025-10-14 18:02:10,581 - main - INFO - ✅ 连接状态已保存
2025-10-14 18:02:10,581 - main - INFO - ✅ 应用已关闭
2025-10-14 18:02:16,774 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:02:16,774 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:02:17,661 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 18:02:17,839 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 18:02:17,839 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '意愿域', '文件域', '证书域', 'wiki域', '费用域', '签署域', '平台功能', 'SaaS域']
2025-10-14 18:02:17,839 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:02:17,839 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:02:17,839 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:02:17,846 - __main__ - INFO - certificate路由加载成功
2025-10-14 18:02:17,848 - __main__ - INFO - fee路由加载成功
2025-10-14 18:02:17,849 - __main__ - INFO - file路由加载成功
2025-10-14 18:02:17,851 - __main__ - INFO - identity路由加载成功
2025-10-14 18:02:17,856 - __main__ - INFO - intention路由加载成功
2025-10-14 18:02:17,869 - __main__ - INFO - platform路由加载成功
2025-10-14 18:02:17,879 - __main__ - INFO - saas路由加载成功
2025-10-14 18:02:17,889 - __main__ - INFO - signing路由加载成功
2025-10-14 18:02:17,891 - __main__ - INFO - wiki路由加载成功
2025-10-14 18:02:17,894 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 18:02:17,894 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', '意愿域', '文件域', '证书域', 'wiki域', '费用域', '签署域', '平台功能', 'SaaS域']
2025-10-14 18:02:17,931 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:02:17,931 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:02:17,932 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:02:17,932 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:02:17,932 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 18:02:17,932 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 18:02:17,932 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:02:17,932 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 18:02:17,932 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 18:02:17,988 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:02:17,989 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:02:17,989 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '意愿域', '文件域', '证书域', 'wiki域', '费用域', '签署域', '平台功能', 'SaaS域']
2025-10-14 18:02:17,989 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:02:17,989 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:02:17,990 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:02:17,997 - main - INFO - certificate路由加载成功
2025-10-14 18:02:17,999 - main - INFO - fee路由加载成功
2025-10-14 18:02:18,001 - main - INFO - file路由加载成功
2025-10-14 18:02:18,004 - main - INFO - identity路由加载成功
2025-10-14 18:02:18,009 - main - INFO - intention路由加载成功
2025-10-14 18:02:18,020 - main - INFO - platform路由加载成功
2025-10-14 18:02:18,029 - main - INFO - saas路由加载成功
2025-10-14 18:02:18,039 - main - INFO - signing路由加载成功
2025-10-14 18:02:18,041 - main - INFO - wiki路由加载成功
2025-10-14 18:02:18,042 - main - INFO - 🚀 设置MCP服务...
2025-10-14 18:02:18,042 - main - INFO - 🏷️ 使用自动发现的标签: ['实名域', '意愿域', '文件域', '证书域', 'wiki域', '费用域', '签署域', '平台功能', 'SaaS域']
2025-10-14 18:02:18,138 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:02:18,139 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:02:18,139 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:02:18,139 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:02:18,139 - main - INFO - 🚀 应用启动中...
2025-10-14 18:02:18,140 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 18:02:18,140 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 18:02:18,140 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 18:02:18,140 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 18:02:18,140 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 18:02:18,140 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 18:04:21,503 - main - INFO - 🛑 应用关闭中...
2025-10-14 18:04:21,504 - main - INFO - ✅ 连接状态已保存
2025-10-14 18:04:21,505 - main - INFO - ✅ 应用已关闭
2025-10-14 18:11:48,419 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:11:48,419 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:11:49,268 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 18:11:49,433 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 18:11:49,434 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', '意愿域', '费用域', '平台功能', '实名域', 'SaaS域', 'wiki域', '文件域', '证书域']
2025-10-14 18:11:49,434 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:11:49,434 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:11:49,434 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:11:49,439 - __main__ - INFO - certificate路由加载成功
2025-10-14 18:11:49,441 - __main__ - INFO - fee路由加载成功
2025-10-14 18:11:49,442 - __main__ - INFO - file路由加载成功
2025-10-14 18:11:49,444 - __main__ - INFO - identity路由加载成功
2025-10-14 18:11:49,448 - __main__ - INFO - intention路由加载成功
2025-10-14 18:11:49,458 - __main__ - INFO - platform路由加载成功
2025-10-14 18:11:49,465 - __main__ - INFO - saas路由加载成功
2025-10-14 18:11:49,475 - __main__ - INFO - signing路由加载成功
2025-10-14 18:11:49,476 - __main__ - INFO - wiki路由加载成功
2025-10-14 18:11:49,479 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 18:11:49,479 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['签署域', '意愿域', '费用域', '平台功能', '实名域', 'SaaS域', 'wiki域', '文件域', '证书域']
2025-10-14 18:11:49,514 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:11:49,514 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:11:49,514 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:11:49,514 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:11:49,514 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 18:11:49,514 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 18:11:49,514 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:11:49,514 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 18:11:49,514 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 18:11:49,566 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:11:49,567 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:11:49,567 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', '意愿域', '费用域', '平台功能', '实名域', 'SaaS域', 'wiki域', '文件域', '证书域']
2025-10-14 18:11:49,567 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:11:49,567 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:11:49,568 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:11:49,574 - main - INFO - certificate路由加载成功
2025-10-14 18:11:49,576 - main - INFO - fee路由加载成功
2025-10-14 18:11:49,577 - main - INFO - file路由加载成功
2025-10-14 18:11:49,578 - main - INFO - identity路由加载成功
2025-10-14 18:11:49,583 - main - INFO - intention路由加载成功
2025-10-14 18:11:49,593 - main - INFO - platform路由加载成功
2025-10-14 18:11:49,601 - main - INFO - saas路由加载成功
2025-10-14 18:11:49,611 - main - INFO - signing路由加载成功
2025-10-14 18:11:49,613 - main - INFO - wiki路由加载成功
2025-10-14 18:11:49,614 - main - INFO - 🚀 设置MCP服务...
2025-10-14 18:11:49,614 - main - INFO - 🏷️ 使用自动发现的标签: ['签署域', '意愿域', '费用域', '平台功能', '实名域', 'SaaS域', 'wiki域', '文件域', '证书域']
2025-10-14 18:11:49,690 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:11:49,690 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:11:49,690 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:11:49,690 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:11:49,691 - main - INFO - 🚀 应用启动中...
2025-10-14 18:11:49,691 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 18:11:49,691 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 18:11:49,691 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 18:11:49,691 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 18:11:49,691 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 18:11:49,692 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 18:16:00,150 - main - INFO - 🛑 应用关闭中...
2025-10-14 18:16:00,151 - main - INFO - ✅ 连接状态已保存
2025-10-14 18:16:00,152 - main - INFO - ✅ 应用已关闭
2025-10-14 18:16:10,470 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:16:10,470 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:16:11,294 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-14 18:16:11,465 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-14 18:16:11,466 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', 'wiki域', '实名域', '签署域', '费用域', '平台功能', '意愿域', '证书域', '文件域']
2025-10-14 18:16:11,466 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:16:11,466 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:16:11,466 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:16:11,472 - __main__ - INFO - certificate路由加载成功
2025-10-14 18:16:11,474 - __main__ - INFO - fee路由加载成功
2025-10-14 18:16:11,475 - __main__ - INFO - file路由加载成功
2025-10-14 18:16:11,476 - __main__ - INFO - identity路由加载成功
2025-10-14 18:16:11,481 - __main__ - INFO - intention路由加载成功
2025-10-14 18:16:11,491 - __main__ - INFO - platform路由加载成功
2025-10-14 18:16:11,498 - __main__ - INFO - saas路由加载成功
2025-10-14 18:16:11,508 - __main__ - INFO - signing路由加载成功
2025-10-14 18:16:11,511 - __main__ - INFO - wiki路由加载成功
2025-10-14 18:16:11,513 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-14 18:16:11,514 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', 'wiki域', '实名域', '签署域', '费用域', '平台功能', '意愿域', '证书域', '文件域']
2025-10-14 18:16:11,547 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:16:11,547 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:16:11,547 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:16:11,547 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:16:11,547 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-14 18:16:11,547 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-14 18:16:11,547 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:16:11,548 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-14 18:16:11,548 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-14 18:16:11,601 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-14 18:16:11,602 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-14 18:16:11,602 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', 'wiki域', '实名域', '签署域', '费用域', '平台功能', '意愿域', '证书域', '文件域']
2025-10-14 18:16:11,602 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-14 18:16:11,602 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-14 18:16:11,602 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-14 18:16:11,610 - main - INFO - certificate路由加载成功
2025-10-14 18:16:11,612 - main - INFO - fee路由加载成功
2025-10-14 18:16:11,613 - main - INFO - file路由加载成功
2025-10-14 18:16:11,614 - main - INFO - identity路由加载成功
2025-10-14 18:16:11,619 - main - INFO - intention路由加载成功
2025-10-14 18:16:11,630 - main - INFO - platform路由加载成功
2025-10-14 18:16:11,638 - main - INFO - saas路由加载成功
2025-10-14 18:16:11,648 - main - INFO - signing路由加载成功
2025-10-14 18:16:11,649 - main - INFO - wiki路由加载成功
2025-10-14 18:16:11,651 - main - INFO - 🚀 设置MCP服务...
2025-10-14 18:16:11,651 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', 'wiki域', '实名域', '签署域', '费用域', '平台功能', '意愿域', '证书域', '文件域']
2025-10-14 18:16:11,730 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-14 18:16:11,730 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-14 18:16:11,730 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-14 18:16:11,730 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-14 18:16:11,731 - main - INFO - 🚀 应用启动中...
2025-10-14 18:16:11,732 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-14 18:16:11,732 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-14 18:16:11,732 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-14 18:16:11,732 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-14 18:16:11,732 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-14 18:16:11,732 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-14 18:16:49,908 - main - INFO - 🛑 应用关闭中...
2025-10-14 18:16:49,910 - main - INFO - ✅ 连接状态已保存
2025-10-14 18:16:49,911 - main - INFO - ✅ 应用已关闭
