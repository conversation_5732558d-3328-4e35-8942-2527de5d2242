2025-10-27 19:34:17,363 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:34:17,366 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:34:18,685 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-27 19:34:18,969 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-27 19:34:18,970 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '平台功能', '文件域', '实名域', '证书域', '费用域', '签署域', 'SaaS域', 'wiki域']
2025-10-27 19:34:18,970 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:34:18,970 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:34:18,970 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:34:18,978 - __main__ - INFO - certificate路由加载成功
2025-10-27 19:34:18,980 - __main__ - INFO - fee路由加载成功
2025-10-27 19:34:18,981 - __main__ - INFO - file路由加载成功
2025-10-27 19:34:18,983 - __main__ - INFO - identity路由加载成功
2025-10-27 19:34:18,989 - __main__ - INFO - intention路由加载成功
2025-10-27 19:34:19,002 - __main__ - INFO - platform路由加载成功
2025-10-27 19:34:19,011 - __main__ - INFO - saas路由加载成功
2025-10-27 19:34:19,022 - __main__ - INFO - signing路由加载成功
2025-10-27 19:34:19,024 - __main__ - INFO - wiki路由加载成功
2025-10-27 19:34:19,028 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-27 19:34:19,028 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '平台功能', '文件域', '实名域', '证书域', '费用域', '签署域', 'SaaS域', 'wiki域']
2025-10-27 19:34:19,069 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:34:19,069 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:34:19,069 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:34:19,069 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:34:19,070 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-27 19:34:19,070 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-27 19:34:19,070 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:34:19,070 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-27 19:34:19,070 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-27 19:34:19,183 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:34:19,184 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:34:19,184 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '平台功能', '文件域', '实名域', '证书域', '费用域', '签署域', 'SaaS域', 'wiki域']
2025-10-27 19:34:19,185 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:34:19,185 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:34:19,185 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:34:19,196 - main - INFO - certificate路由加载成功
2025-10-27 19:34:19,198 - main - INFO - fee路由加载成功
2025-10-27 19:34:19,200 - main - INFO - file路由加载成功
2025-10-27 19:34:19,203 - main - INFO - identity路由加载成功
2025-10-27 19:34:19,212 - main - INFO - intention路由加载成功
2025-10-27 19:34:19,231 - main - INFO - platform路由加载成功
2025-10-27 19:34:19,248 - main - INFO - saas路由加载成功
2025-10-27 19:34:19,267 - main - INFO - signing路由加载成功
2025-10-27 19:34:19,272 - main - INFO - wiki路由加载成功
2025-10-27 19:34:19,275 - main - INFO - 🚀 设置MCP服务...
2025-10-27 19:34:19,275 - main - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '平台功能', '文件域', '实名域', '证书域', '费用域', '签署域', 'SaaS域', 'wiki域']
2025-10-27 19:34:19,366 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:34:19,366 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:34:19,366 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:34:19,366 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:34:19,367 - main - INFO - 🚀 应用启动中...
2025-10-27 19:34:19,367 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-27 19:34:19,368 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-27 19:34:19,368 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-27 19:34:19,368 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-27 19:34:19,369 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-27 19:34:19,369 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-27 19:34:41,525 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:35:01,526 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:35:21,540 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:35:24,935 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 19:35:24,963 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'签署人类型': '个人', '签署区样式': '单页', '签署区类型': '普通'}], group=default, FDA=False
2025-10-27 19:35:25,876 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "984bcad0666c4d329658586f86e1e9f0"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 141.**************, "positionY": 541.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}]}
2025-10-27 19:35:25,877 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "5beae2535d284de9b8235d9bc0f73d90"}}
2025-10-27 19:35:25,877 - mcpService.domains.signing_service - INFO - 获取的flowId为：5beae2535d284de9b8235d9bc0f73d90
2025-10-27 19:35:26,247 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/1WYN0QMk
2025-10-27 19:35:26,247 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T19:35:26.247487', 'data': {'flowId': '5beae2535d284de9b8235d9bc0f73d90', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=bhazaYOD40&flowId=5beae2535d284de9b8235d9bc0f73d90&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N6ZgAXb5xOwV7wFiGeDl1hlEucbdngYh4h5kVe06ifSYeSRF0hYVUz2CMhQYyjZeKtIdTzq1C%2FZuNTSf2sXXZlCBkFfKkv2ZScLOOGRfBYjb8ZWGXQUlG7n309aSllzyLOx3JPkef3rv9k9nWPRqEVab6ahhn1h7PWs6ZX3P2iZXDndbSc34Ej%2B67DwxmkA7hHTAQdaKaCTDl%2FFQ6dJmlTaZW9z1zyJnLesadIxBSPcs', 'shortUrl': 'https://testt.tsign.cn/1WYN0QMk'}}
2025-10-27 19:35:26,248 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 19:35:41,527 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:36:01,537 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:36:21,533 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:36:41,533 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:37:01,579 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:37:21,537 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:37:41,527 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:38:01,534 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:38:21,529 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:38:29,761 - main - INFO - 🛑 应用关闭中...
2025-10-27 19:38:29,763 - main - INFO - ✅ 连接状态已保存
2025-10-27 19:38:29,763 - main - INFO - ✅ 应用已关闭
2025-10-27 19:38:31,813 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:38:31,814 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:38:32,722 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-27 19:38:32,913 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-27 19:38:32,913 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '费用域', 'wiki域', '证书域', '签署域', '文件域', '意愿域', '平台功能', '实名域']
2025-10-27 19:38:32,914 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:38:32,914 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:38:32,914 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:38:32,921 - __main__ - INFO - certificate路由加载成功
2025-10-27 19:38:32,924 - __main__ - INFO - fee路由加载成功
2025-10-27 19:38:32,926 - __main__ - INFO - file路由加载成功
2025-10-27 19:38:32,928 - __main__ - INFO - identity路由加载成功
2025-10-27 19:38:32,935 - __main__ - INFO - intention路由加载成功
2025-10-27 19:38:32,952 - __main__ - INFO - platform路由加载成功
2025-10-27 19:38:32,961 - __main__ - INFO - saas路由加载成功
2025-10-27 19:38:32,973 - __main__ - INFO - signing路由加载成功
2025-10-27 19:38:32,976 - __main__ - INFO - wiki路由加载成功
2025-10-27 19:38:32,980 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-27 19:38:32,980 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '费用域', 'wiki域', '证书域', '签署域', '文件域', '意愿域', '平台功能', '实名域']
2025-10-27 19:38:33,018 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:38:33,018 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:38:33,018 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:38:33,018 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:33,018 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-27 19:38:33,018 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-27 19:38:33,018 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:33,018 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-27 19:38:33,018 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-27 19:38:33,085 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:38:33,086 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:38:33,086 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '费用域', 'wiki域', '证书域', '签署域', '文件域', '意愿域', '平台功能', '实名域']
2025-10-27 19:38:33,086 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:38:33,087 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:38:33,087 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:38:33,094 - main - INFO - certificate路由加载成功
2025-10-27 19:38:33,096 - main - INFO - fee路由加载成功
2025-10-27 19:38:33,098 - main - INFO - file路由加载成功
2025-10-27 19:38:33,099 - main - INFO - identity路由加载成功
2025-10-27 19:38:33,105 - main - INFO - intention路由加载成功
2025-10-27 19:38:33,117 - main - INFO - platform路由加载成功
2025-10-27 19:38:33,127 - main - INFO - saas路由加载成功
2025-10-27 19:38:33,139 - main - INFO - signing路由加载成功
2025-10-27 19:38:33,141 - main - INFO - wiki路由加载成功
2025-10-27 19:38:33,144 - main - INFO - 🚀 设置MCP服务...
2025-10-27 19:38:33,144 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '费用域', 'wiki域', '证书域', '签署域', '文件域', '意愿域', '平台功能', '实名域']
2025-10-27 19:38:33,239 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:38:33,239 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:38:33,239 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:38:33,239 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:33,239 - main - INFO - 🚀 应用启动中...
2025-10-27 19:38:33,240 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-27 19:38:33,240 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-27 19:38:33,240 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-27 19:38:33,240 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-27 19:38:33,240 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-27 19:38:33,240 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-27 19:38:39,150 - main - INFO - 🛑 应用关闭中...
2025-10-27 19:38:39,151 - main - INFO - ✅ 连接状态已保存
2025-10-27 19:38:39,152 - main - INFO - ✅ 应用已关闭
2025-10-27 19:38:42,036 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:38:42,037 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:38:42,962 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-27 19:38:43,160 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-27 19:38:43,160 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '文件域', '实名域', '平台功能', 'wiki域', 'SaaS域', '费用域', '签署域', '意愿域']
2025-10-27 19:38:43,160 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:38:43,160 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:38:43,160 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:38:43,166 - __main__ - INFO - certificate路由加载成功
2025-10-27 19:38:43,168 - __main__ - INFO - fee路由加载成功
2025-10-27 19:38:43,169 - __main__ - INFO - file路由加载成功
2025-10-27 19:38:43,172 - __main__ - INFO - identity路由加载成功
2025-10-27 19:38:43,176 - __main__ - INFO - intention路由加载成功
2025-10-27 19:38:43,187 - __main__ - INFO - platform路由加载成功
2025-10-27 19:38:43,195 - __main__ - INFO - saas路由加载成功
2025-10-27 19:38:43,205 - __main__ - INFO - signing路由加载成功
2025-10-27 19:38:43,207 - __main__ - INFO - wiki路由加载成功
2025-10-27 19:38:43,209 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-27 19:38:43,209 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '文件域', '实名域', '平台功能', 'wiki域', 'SaaS域', '费用域', '签署域', '意愿域']
2025-10-27 19:38:43,244 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:38:43,244 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:38:43,244 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:38:43,244 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:43,244 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-27 19:38:43,244 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-27 19:38:43,244 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:43,244 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-27 19:38:43,244 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-27 19:38:43,299 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:38:43,300 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:38:43,300 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '文件域', '实名域', '平台功能', 'wiki域', 'SaaS域', '费用域', '签署域', '意愿域']
2025-10-27 19:38:43,300 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:38:43,300 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:38:43,300 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:38:43,306 - main - INFO - certificate路由加载成功
2025-10-27 19:38:43,308 - main - INFO - fee路由加载成功
2025-10-27 19:38:43,309 - main - INFO - file路由加载成功
2025-10-27 19:38:43,310 - main - INFO - identity路由加载成功
2025-10-27 19:38:43,315 - main - INFO - intention路由加载成功
2025-10-27 19:38:43,326 - main - INFO - platform路由加载成功
2025-10-27 19:38:43,334 - main - INFO - saas路由加载成功
2025-10-27 19:38:43,343 - main - INFO - signing路由加载成功
2025-10-27 19:38:43,345 - main - INFO - wiki路由加载成功
2025-10-27 19:38:43,346 - main - INFO - 🚀 设置MCP服务...
2025-10-27 19:38:43,346 - main - INFO - 🏷️ 使用自动发现的标签: ['证书域', '文件域', '实名域', '平台功能', 'wiki域', 'SaaS域', '费用域', '签署域', '意愿域']
2025-10-27 19:38:43,437 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:38:43,437 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:38:43,437 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:38:43,437 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:38:43,438 - main - INFO - 🚀 应用启动中...
2025-10-27 19:38:43,439 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-27 19:38:43,439 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-27 19:38:43,439 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-27 19:38:43,439 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-27 19:38:43,439 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-27 19:38:43,439 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-27 19:39:02,055 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:39:22,063 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:39:40,698 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 19:39:40,729 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=None, group=default, FDA=False
2025-10-27 19:39:41,459 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "984bcad0666c4d329658586f86e1e9f0"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 475.*************, "positionY": 608.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}]}
2025-10-27 19:39:41,460 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "ec8fba1156384103973f55bbf4feb175"}}
2025-10-27 19:39:41,460 - mcpService.domains.signing_service - INFO - 获取的flowId为：ec8fba1156384103973f55bbf4feb175
2025-10-27 19:39:41,816 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/FLl6gBGM
2025-10-27 19:39:41,816 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T19:39:41.816636', 'data': {'flowId': 'ec8fba1156384103973f55bbf4feb175', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=mWc8cyk4bW&flowId=ec8fba1156384103973f55bbf4feb175&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N6ZgAXb5xOwV7wFiGeDl1hlEucbdngYh4h5kVe06ifSYeSRF0hYVUz2CMhQYyjZeKtIdTzq1C%2FZuNTSf2sXXZlCBkFfKkv2ZScLOOGRfBYjb8ZWGXQUlG7n309aSllzyLOx3JPkef3rv9k9nWPRqEVab6ahhn1h7PWs6ZX3P2iZXJ2tTznuY8dGFRM%2BP2atAaTfss52dFIeBoEFVWFH0z%2B6g6AKHimy3bcpowv2iSZfX', 'shortUrl': 'https://testt.tsign.cn/FLl6gBGM'}}
2025-10-27 19:39:41,816 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 19:39:42,058 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:40:02,060 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:40:22,060 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:40:42,057 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:41:02,071 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:41:22,059 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:41:42,060 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:42:02,065 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:42:04,665 - main - INFO - 🛑 应用关闭中...
2025-10-27 19:42:04,667 - main - INFO - ✅ 连接状态已保存
2025-10-27 19:42:04,667 - main - INFO - ✅ 应用已关闭
2025-10-27 19:43:14,562 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:43:14,562 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:43:15,476 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-27 19:43:15,679 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-27 19:43:15,679 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '证书域', '签署域', '平台功能', '费用域', '实名域', '意愿域', 'wiki域', '文件域']
2025-10-27 19:43:15,679 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:43:15,679 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:43:15,679 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:43:15,685 - __main__ - INFO - certificate路由加载成功
2025-10-27 19:43:15,687 - __main__ - INFO - fee路由加载成功
2025-10-27 19:43:15,688 - __main__ - INFO - file路由加载成功
2025-10-27 19:43:15,701 - __main__ - INFO - identity路由加载成功
2025-10-27 19:43:15,706 - __main__ - INFO - intention路由加载成功
2025-10-27 19:43:15,717 - __main__ - INFO - platform路由加载成功
2025-10-27 19:43:15,725 - __main__ - INFO - saas路由加载成功
2025-10-27 19:43:15,734 - __main__ - INFO - signing路由加载成功
2025-10-27 19:43:15,735 - __main__ - INFO - wiki路由加载成功
2025-10-27 19:43:15,738 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-27 19:43:15,738 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '证书域', '签署域', '平台功能', '费用域', '实名域', '意愿域', 'wiki域', '文件域']
2025-10-27 19:43:15,771 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:43:15,771 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:43:15,771 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:43:15,771 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:43:15,771 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-27 19:43:15,771 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-27 19:43:15,771 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:43:15,771 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-27 19:43:15,772 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-27 19:43:15,823 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 19:43:15,824 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 19:43:15,824 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '证书域', '签署域', '平台功能', '费用域', '实名域', '意愿域', 'wiki域', '文件域']
2025-10-27 19:43:15,824 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 19:43:15,824 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 19:43:15,825 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 19:43:15,831 - main - INFO - certificate路由加载成功
2025-10-27 19:43:15,833 - main - INFO - fee路由加载成功
2025-10-27 19:43:15,834 - main - INFO - file路由加载成功
2025-10-27 19:43:15,836 - main - INFO - identity路由加载成功
2025-10-27 19:43:15,840 - main - INFO - intention路由加载成功
2025-10-27 19:43:15,851 - main - INFO - platform路由加载成功
2025-10-27 19:43:15,859 - main - INFO - saas路由加载成功
2025-10-27 19:43:15,869 - main - INFO - signing路由加载成功
2025-10-27 19:43:15,871 - main - INFO - wiki路由加载成功
2025-10-27 19:43:15,873 - main - INFO - 🚀 设置MCP服务...
2025-10-27 19:43:15,873 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '证书域', '签署域', '平台功能', '费用域', '实名域', '意愿域', 'wiki域', '文件域']
2025-10-27 19:43:15,963 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 19:43:15,963 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 19:43:15,963 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 19:43:15,963 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 19:43:15,964 - main - INFO - 🚀 应用启动中...
2025-10-27 19:43:15,964 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-27 19:43:15,965 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-27 19:43:15,965 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-27 19:43:15,965 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-27 19:43:15,965 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-27 19:43:15,965 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-27 19:43:22,072 - fastapi_mcp.transport.sse - WARNING - Could not find session for ID: 56bba527-87ef-4934-af04-b2158b4904e0
2025-10-27 19:43:22,113 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:43:42,130 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:43:56,665 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:43:58,608 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:43:59,488 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 19:44:18,255 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 19:44:18,285 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'签署人类型': '个人', '签署区样式': '单页', '签署区类型': '普通'}], group=default, FDA=False
2025-10-27 19:44:20,092 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "984bcad0666c4d329658586f86e1e9f0"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signFlowInitiator": {"psnInitiator": {"psnId": "7caf2f2e028d46d3817f111090d37e99"}}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 178.*************, "positionY": 501.**************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}]}
2025-10-27 19:44:20,093 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "50f178e60be8436284fe157cf0676916"}}
2025-10-27 19:44:20,093 - mcpService.domains.signing_service - INFO - 获取的flowId为：50f178e60be8436284fe157cf0676916
2025-10-27 19:44:23,468 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/zUhNWogt
2025-10-27 19:44:23,468 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T19:44:23.468346', 'data': {'flowId': '50f178e60be8436284fe157cf0676916', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=n8Rs2gI6Uk&flowId=50f178e60be8436284fe157cf0676916&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2Nx9vRL%2FpCGL81AuRwnkHGQ5xIIGEvwwgi7pFOKmTZH7XsZiam9JP3S6ODn2HBdJIaraZYwY6y6QktODL4cFKlCv51EwJfogZaWjhdAuv4PUblYYTTJ0mEhtyyDwlfXZvKxrHZamcE6cb9p16iTn0acsaXg2QDbh5PmPVXeRuvZzfI8ZZwVwIXorqO27TMZiudnS23q19mjmec1VXpNo2aKmeHrSCh2ca9dtATdf%2Bwkaf', 'shortUrl': 'https://testt.tsign.cn/zUhNWogt'}}
2025-10-27 19:44:23,469 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 19:44:23,471 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:44:39,489 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 19:44:53,841 - main - INFO - 🛑 应用关闭中...
2025-10-27 19:44:53,843 - main - INFO - ✅ 连接状态已保存
2025-10-27 19:44:53,843 - main - INFO - ✅ 应用已关闭
2025-10-27 20:27:44,231 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 20:27:44,232 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 20:27:45,235 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-10-27 20:27:45,434 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-10-27 20:27:45,434 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '实名域', '文件域', 'wiki域', 'SaaS域', '费用域', '平台功能', '证书域', '签署域']
2025-10-27 20:27:45,434 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 20:27:45,434 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 20:27:45,435 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 20:27:45,441 - __main__ - INFO - certificate路由加载成功
2025-10-27 20:27:45,444 - __main__ - INFO - fee路由加载成功
2025-10-27 20:27:45,446 - __main__ - INFO - file路由加载成功
2025-10-27 20:27:45,449 - __main__ - INFO - identity路由加载成功
2025-10-27 20:27:45,453 - __main__ - INFO - intention路由加载成功
2025-10-27 20:27:45,467 - __main__ - INFO - platform路由加载成功
2025-10-27 20:27:45,476 - __main__ - INFO - saas路由加载成功
2025-10-27 20:27:45,488 - __main__ - INFO - signing路由加载成功
2025-10-27 20:27:45,490 - __main__ - INFO - wiki路由加载成功
2025-10-27 20:27:45,492 - __main__ - INFO - 🚀 设置MCP服务...
2025-10-27 20:27:45,492 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '实名域', '文件域', 'wiki域', 'SaaS域', '费用域', '平台功能', '证书域', '签署域']
2025-10-27 20:27:45,531 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 20:27:45,531 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 20:27:45,531 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 20:27:45,531 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 20:27:45,531 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-10-27 20:27:45,531 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-10-27 20:27:45,532 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 20:27:45,532 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-10-27 20:27:45,532 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-10-27 20:27:45,589 - app.core.auto_discovery - INFO - 开始自动发现...
2025-10-27 20:27:45,590 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-10-27 20:27:45,590 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', '实名域', '文件域', 'wiki域', 'SaaS域', '费用域', '平台功能', '证书域', '签署域']
2025-10-27 20:27:45,590 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-10-27 20:27:45,591 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-10-27 20:27:45,591 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-10-27 20:27:45,598 - main - INFO - certificate路由加载成功
2025-10-27 20:27:45,600 - main - INFO - fee路由加载成功
2025-10-27 20:27:45,601 - main - INFO - file路由加载成功
2025-10-27 20:27:45,604 - main - INFO - identity路由加载成功
2025-10-27 20:27:45,614 - main - INFO - intention路由加载成功
2025-10-27 20:27:45,626 - main - INFO - platform路由加载成功
2025-10-27 20:27:45,635 - main - INFO - saas路由加载成功
2025-10-27 20:27:45,649 - main - INFO - signing路由加载成功
2025-10-27 20:27:45,651 - main - INFO - wiki路由加载成功
2025-10-27 20:27:45,654 - main - INFO - 🚀 设置MCP服务...
2025-10-27 20:27:45,654 - main - INFO - 🏷️ 使用自动发现的标签: ['意愿域', '实名域', '文件域', 'wiki域', 'SaaS域', '费用域', '平台功能', '证书域', '签署域']
2025-10-27 20:27:45,762 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-10-27 20:27:45,762 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-10-27 20:27:45,762 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-10-27 20:27:45,763 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-10-27 20:27:45,763 - main - INFO - 🚀 应用启动中...
2025-10-27 20:27:45,764 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-10-27 20:27:45,764 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-10-27 20:27:45,764 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-10-27 20:27:45,764 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-10-27 20:27:45,764 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-10-27 20:27:45,764 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-10-27 20:27:57,652 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-10-27 20:28:17,660 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:28:37,665 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:28:57,658 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:29:02,377 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 20:29:02,405 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'手机号': '***********', '签署人类型': '个人', '签署区样式': '自由签', '顺序': 1}, {'企业名称': '浙江金华市希创信息技术有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': 'esigntest计费测试企业', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': '杭州天谷信息科技有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '骑缝签', '顺序': 2}], group=iterationDev, FDA=False
2025-10-27 20:29:03,414 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "faa68ff796c44e30a17ea87a6343dbe2"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "faa68ff796c44e30a17ea87a6343dbe2", "normalSignFieldConfig": {"autoSign": false, "freeMode": true, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 410.**************, "positionY": 488.**************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}, {"orgSignerInfo": {"orgName": "\u6d59\u6c5f\u91d1\u534e\u5e02\u5e0c\u521b\u4fe1\u606f\u6280\u672f\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "faa68ff796c44e30a17ea87a6343dbe2", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 138.*************, "positionY": 618.************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "esigntest\u8ba1\u8d39\u6d4b\u8bd5\u4f01\u4e1a", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "faa68ff796c44e30a17ea87a6343dbe2", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 184.*************, "positionY": 489.**************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "\u676d\u5dde\u5929\u8c37\u4fe1\u606f\u79d1\u6280\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "faa68ff796c44e30a17ea87a6343dbe2", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 281.**************, "positionY": 642.*************}, "signFieldStyle": 2}, "signFieldType": 0}], "signerType": 1}]}
2025-10-27 20:29:03,414 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "5299408b50794dc68773d08608243152"}}
2025-10-27 20:29:03,414 - mcpService.domains.signing_service - INFO - 获取的flowId为：5299408b50794dc68773d08608243152
2025-10-27 20:29:03,726 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/iBvNL2Eb
2025-10-27 20:29:03,727 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T20:29:03.727579', 'data': {'flowId': '5299408b50794dc68773d08608243152', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=MWszkDaNl3&flowId=5299408b50794dc68773d08608243152&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N6ZgAXb5xOwV7wFiGeDl1hlEucbdngYh4h5kVe06ifSYeSRF0hYVUz2CMhQYyjZeKtIdTzq1C%2FZuNTSf2sXXZlCBkFfKkv2ZScLOOGRfBYjb8ZWGXQUlG7n309aSllzyLOx3JPkef3rv9k9nWPRqEVab6ahhn1h7PWs6ZX3P2iZXH%2BsKxOXs6MX4Eisk8aEQs5yxlI%2FtDfP11IqtjCtctOtnXbsmdk8PhgFuvjRLEOpT', 'shortUrl': 'https://testt.tsign.cn/iBvNL2Eb'}}
2025-10-27 20:29:03,727 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 20:29:17,666 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:29:37,665 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:29:57,659 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:30:07,321 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 20:30:07,360 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'手机号': '***********', '签署人类型': '个人', '签署区样式': '自由签', '顺序': 1}, {'企业名称': '浙江金华市希创信息技术有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': 'esigntest计费测试企业', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': '杭州天谷信息科技有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '骑缝签', '顺序': 2}], group=iterationDev, FDA=False
2025-10-27 20:30:07,918 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "id31be3d766dbf4a9da8dbe85a434b7d67"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "id31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": true, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 468.*************, "positionY": 654.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}, {"orgSignerInfo": {"orgName": "\u6d59\u6c5f\u91d1\u534e\u5e02\u5e0c\u521b\u4fe1\u606f\u6280\u672f\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "id31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 167.**************, "positionY": 607.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "esigntest\u8ba1\u8d39\u6d4b\u8bd5\u4f01\u4e1a", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "id31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 294.*************, "positionY": 579.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "\u676d\u5dde\u5929\u8c37\u4fe1\u606f\u79d1\u6280\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "id31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 117.**************, "positionY": 663.*************}, "signFieldStyle": 2}, "signFieldType": 0}], "signerType": 1}]}
2025-10-27 20:30:07,920 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 1437511, "message": "\u6587\u6863\u4e0d\u5b58\u5728\uff1aid31be3d766dbf4a9da8dbe85a434b7d67", "data": null}
2025-10-27 20:30:07,921 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'error', 'message': '创建签署流程失败！', 'timestamp': '2025-10-27T20:30:07.921246', 'details': {'code': 1437511, 'message': '文档不存在：id31be3d766dbf4a9da8dbe85a434b7d67', 'data': None}}
2025-10-27 20:30:07,923 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 20:30:17,670 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:30:37,652 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:30:51,334 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 20:30:51,362 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'手机号': '***********', '签署人类型': '个人', '签署区样式': '自由签', '顺序': 1}, {'企业名称': '浙江金华市希创信息技术有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': 'esigntest计费测试企业', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': '杭州天谷信息科技有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '骑缝签', '顺序': 2}], group=iterationDev, FDA=False
2025-10-27 20:30:52,425 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "31be3d766dbf4a9da8dbe85a434b7d67"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": true, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 276.*************, "positionY": 406.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}, {"orgSignerInfo": {"orgName": "\u6d59\u6c5f\u91d1\u534e\u5e02\u5e0c\u521b\u4fe1\u606f\u6280\u672f\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 306.*************, "positionY": 644.************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "esigntest\u8ba1\u8d39\u6d4b\u8bd5\u4f01\u4e1a", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 472.************, "positionY": 580.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "\u676d\u5dde\u5929\u8c37\u4fe1\u606f\u79d1\u6280\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "31be3d766dbf4a9da8dbe85a434b7d67", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 161.**************, "positionY": 590.*************}, "signFieldStyle": 2}, "signFieldType": 0}], "signerType": 1}]}
2025-10-27 20:30:52,426 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "9802d5ea7dec4035945e20be5d635156"}}
2025-10-27 20:30:52,427 - mcpService.domains.signing_service - INFO - 获取的flowId为：9802d5ea7dec4035945e20be5d635156
2025-10-27 20:30:52,828 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/1LIjhitv
2025-10-27 20:30:52,828 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T20:30:52.828814', 'data': {'flowId': '9802d5ea7dec4035945e20be5d635156', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=ikgQD0eMrg&flowId=9802d5ea7dec4035945e20be5d635156&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N6ZgAXb5xOwV7wFiGeDl1hlEucbdngYh4h5kVe06ifSYeSRF0hYVUz2CMhQYyjZeKtIdTzq1C%2FZuNTSf2sXXZlCBkFfKkv2ZScLOOGRfBYjb8ZWGXQUlG7n309aSllzyLOx3JPkef3rv9k9nWPRqEVab6ahhn1h7PWs6ZX3P2iZXIFwuMxbS9iFZr4kDUmmjKb5DYQgvweBN4NsErPAb7ymI81bL%2FamjtpZAPQOOMsjy', 'shortUrl': 'https://testt.tsign.cn/1LIjhitv'}}
2025-10-27 20:30:52,829 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 20:30:57,653 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:31:17,663 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:31:37,666 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:31:57,665 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:32:02,001 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-10-27 20:32:02,035 - app.mcpController.domains.signing_controller - INFO - 获取签署链接请求: environment=测试环境, signer_list=[{'手机号': '***********', '签署人类型': '个人', '签署区样式': '自由签', '顺序': 1}, {'企业名称': '浙江金华市希创信息技术有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': 'esigntest计费测试企业', '手机号': '***********', '签署人类型': '企业', '签署区样式': '单页签', '顺序': 2}, {'企业名称': '杭州天谷信息科技有限公司', '手机号': '***********', '签署人类型': '企业', '签署区样式': '骑缝签', '顺序': 2}], group=iterationDev, FDA=False
2025-10-27 20:32:03,085 - mcpService.domains.signing_service - INFO - 请求参数为：{"docs": [{"fileId": "984bcad0666c4d329658586f86e1e9f0"}], "signFlowConfig": {"autoFinish": true, "autoStart": true, "identityVerify": false, "signFlowTitle": "MCP\u53d1\u8d77\u7684\u6d41\u7a0b"}, "signers": [{"psnSignerInfo": {"psnAccount": "***********"}, "signConfig": {"forcedReadingTime": 0, "signOrder": 1, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": true, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 386.*************, "positionY": 509.**************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 0}, {"orgSignerInfo": {"orgName": "\u6d59\u6c5f\u91d1\u534e\u5e02\u5e0c\u521b\u4fe1\u606f\u6280\u672f\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 376.**************, "positionY": 553.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "esigntest\u8ba1\u8d39\u6d4b\u8bd5\u4f01\u4e1a", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 142.**************, "positionY": 551.*************}, "signFieldStyle": 1}, "signFieldType": 0}], "signerType": 1}, {"orgSignerInfo": {"orgName": "\u676d\u5dde\u5929\u8c37\u4fe1\u606f\u79d1\u6280\u6709\u9650\u516c\u53f8", "transactorInfo": {"psnAccount": "***********"}}, "signConfig": {"forcedReadingTime": 0, "signOrder": 2, "signTaskType": 0}, "signFields": [{"customBizNum": "", "fileId": "984bcad0666c4d329658586f86e1e9f0", "normalSignFieldConfig": {"autoSign": false, "freeMode": false, "movableSignField": true, "psnSealStyles": "", "signFieldPosition": {"acrossPageMode": "ALL", "acrossPageOffset": 0, "positionPage": "1", "positionX": 318.**************, "positionY": 442.**************}, "signFieldStyle": 2}, "signFieldType": 0}], "signerType": 1}]}
2025-10-27 20:32:03,086 - mcpService.domains.signing_service - INFO - 返回结果为：{"code": 0, "message": "\u6210\u529f", "data": {"signFlowId": "05edbd01bcda4b1ebbeb7331256dc580"}}
2025-10-27 20:32:03,086 - mcpService.domains.signing_service - INFO - 获取的flowId为：05edbd01bcda4b1ebbeb7331256dc580
2025-10-27 20:32:03,508 - mcpService.domains.signing_service - INFO - 获取签署链接：https://testt.tsign.cn/jJh95nDX
2025-10-27 20:32:03,509 - app.mcpController.domains.signing_controller - INFO - 获取签署链接完成: {'status': 'success', 'message': '成功获取签署链接', 'timestamp': '2025-10-27T20:32:03.509134', 'data': {'flowId': '05edbd01bcda4b1ebbeb7331256dc580', 'signUrl': 'http://testh5.tsign.cn/mesign/guide?context=A8kRUwznLB&flowId=05edbd01bcda4b1ebbeb7331256dc580&organ=false&appId=7876611670&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N6ZgAXb5xOwV7wFiGeDl1hlEucbdngYh4h5kVe06ifSYeSRF0hYVUz2CMhQYyjZeKtIdTzq1C%2FZuNTSf2sXXZlCBkFfKkv2ZScLOOGRfBYjb8ZWGXQUlG7n309aSllzyLOx3JPkef3rv9k9nWPRqEVab6ahhn1h7PWs6ZX3P2iZXdUp3v7dNkmialUldlkBiWStGCT7sw6mfD08cgEG%2FvgY1rnF5hsCNxTEfNg2cEKmf', 'shortUrl': 'https://testt.tsign.cn/jJh95nDX'}}
2025-10-27 20:32:03,510 - httpx - INFO - HTTP Request: POST http://apiserver/signing/create_sign_flow_by_file "HTTP/1.1 200 OK"
2025-10-27 20:32:17,653 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:32:37,662 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:32:57,665 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:33:17,655 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-10-27 20:33:29,158 - main - INFO - 🛑 应用关闭中...
2025-10-27 20:33:29,162 - main - INFO - ✅ 连接状态已保存
2025-10-27 20:33:29,163 - main - INFO - ✅ 应用已关闭
