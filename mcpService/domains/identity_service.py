#!/usr/bin/env python3
"""
实名域服务 - 实名认证相关功能实现
提供实名认证、身份验证等功能
"""
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class IdentityService(BaseService):
    """实名服务类"""
    
    def __init__(self):
        super().__init__(domain="identity")


# 全局实名服务实例
identity_service = IdentityService()


async def create_identity_verification(
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建实名认证
    
    Args:
        environment: 环境描述，支持自然语言
        
    Returns:
        创建结果
    """
    try:
        
        # 步骤1: 获取测试账号信息

        test_account_result = await _get_test_account(environment)
        if test_account_result.get("status") != "success":
            return test_account_result

        logger.info(f"调用_get_test_account函数，入参 environment: {environment}")

        test_account = test_account_result.get("data", {})
        personal_name = test_account.get('name')
        personal_mobile = test_account.get('phone')
        personal_idcard = test_account.get('idcard')
        
        # 添加调试信息
        logger.info(f"步骤1 - 获取测试账号: name={personal_name}, mobile={personal_mobile}, idcard={personal_idcard}")
        
        # 步骤2: 创建标准签个人账号
        personal_account_result = await _create_individual_account(
            personal_name, personal_idcard, personal_mobile, environment
        )
        if personal_account_result.get("status") != "success":
            return personal_account_result
            
        # 处理创建个人账号的返回数据
        personal_account_data = personal_account_result.get("data", {})
        personal_account_id = personal_account_data.get("accountId")
            
        if not personal_account_id:
            return identity_service.formatter.error(
                message="创建个人账号失败，未返回accountId",
                details=personal_account_result
            )
            
        logger.info(f"步骤2 - 创建个人账号成功: accountId={personal_account_id}")
        
        # 步骤3: 发起运营商三要素实名认证
        telecom_verification_result = await _initiate_telecom_verification(
            personal_account_id, personal_mobile, environment
        )
        if telecom_verification_result.get("status") != "success":
            return telecom_verification_result
            
        # 检查运营商认证是否成功并提取flowId
        telecom_data = telecom_verification_result.get("data", {})
        if not (isinstance(telecom_data, dict) and telecom_data.get("code") == 0 and telecom_data.get("message") == "成功"):
            return identity_service.formatter.error(
                message="发起运营商三要素实名认证失败",
                details=telecom_verification_result
            )
            
        # 提取flowId
        nested_data = telecom_data.get("data", {})
        flow_id_1 = nested_data.get("flowId") if isinstance(nested_data, dict) else None
        if not flow_id_1:
            return identity_service.formatter.error(
                message="发起运营商认证失败，未返回flowId",
                details=telecom_verification_result
            )
            
        logger.info(f"步骤3 - 发起运营商认证成功: flowId={flow_id_1}")
            
        # 步骤4: 验证手机号验证码（测试环境中默认为123456）
        verify_code_result = await _verify_mobile_code(flow_id_1, "123456", environment)
        
        # 检查验证码校验结果
        verify_code_data = verify_code_result.get("data", {})
        # 检查是否有明确的错误信息
        if isinstance(verify_code_data, dict):
            code = verify_code_data.get("code")
            message = verify_code_data.get("message")
            # 检查验证码校验是否成功
            if not (code == 0 and message == "成功"):
                return identity_service.formatter.error(
                    message="手机号验证码校验失败",
                    details=verify_code_result
                )
            else:
                logger.info(f"步骤4 - 验证码校验成功: flowId={flow_id_1}")

        # 步骤5: 创建标准签企业账号
        enterprise_name = test_account.get('orgName', '')
        legal_name = personal_name
        legal_idno = personal_idcard
        uscc_code = test_account.get('orgCode')
        
        enterprise_account_result = await _create_enterprise_account(
            personal_account_id, enterprise_name, legal_name, legal_idno, uscc_code, environment
        )
        if enterprise_account_result.get("status") != "success":
            return enterprise_account_result
            
        # 处理创建企业账号的返回数据（正确提取orgId）
        enterprise_data = enterprise_account_result.get("data", {})
        org_id = enterprise_data.get("orgId")
        
        # 如果第一种方式没获取到，尝试从嵌套的data中获取
        if not org_id:
            nested_data = enterprise_data.get("data", {})
            if isinstance(nested_data, dict):
                org_id = nested_data.get("orgId")
        
        # 检查org_id是否成功提取
        if not org_id:
            return identity_service.formatter.error(
                message="创建企业账号失败，未返回orgId",
                details=enterprise_account_result
            )
            
        # 在模拟环境中添加单用户通道配置
        if environment and ("模拟" in environment or "sml" in environment.lower()):
            add_route_result = await _add_single_user_channel(
                enterprise_name, uscc_code, environment
            )
            if add_route_result.get("status") != "success":
                return add_route_result

        
        # 步骤6: 三要素发起企业实名
        enterprise_verification_result = await _initiate_enterprise_verification(
            org_id, personal_account_id, environment
        )
        if enterprise_verification_result.get("status") != "success":
            return enterprise_verification_result
            
        # 步骤7: 发起随机金额打款
        # 处理企业认证返回数据并提取flowId
        enterprise_verification_data = enterprise_verification_result.get("data", {})
        flow_id_2 = enterprise_verification_data.get("flowId")
        
        # 如果第一种方式没获取到，尝试从嵌套的data中获取
        if not flow_id_2:
            nested_data = enterprise_verification_data.get("data", {})
            if isinstance(nested_data, dict):
                flow_id_2 = nested_data.get("flowId")
            
        if not flow_id_2:
            return identity_service.formatter.error(
                message="发起企业认证失败，未返回flowId",
                details=enterprise_verification_result
            )
        
        # 发起随机金额打款
        transfer_result = await _initiate_transfer_payment(
            flow_id_2, environment
        )
        if transfer_result.get("status") != "success":
            return transfer_result
            
        # 步骤8: 随机金额校验
        verify_amount_result = await _verify_random_amount(
            flow_id_2, environment
        )
        if verify_amount_result.get("status") != "success":
            return verify_amount_result
            
        # 步骤9: 查询随机金额打款进度
        transfer_process_result = await _query_transfer_process(
            flow_id_2, environment
        )
        if transfer_process_result.get("status") != "success":
            return transfer_process_result
            
        # 检查打款进度是否完成
        process_data = transfer_process_result.get("data", {})
        # 处理嵌套的数据结构
        if isinstance(process_data, dict) and "data" in process_data:
            nested_data = process_data.get("data", {})
            process_status = nested_data.get("process") if isinstance(nested_data, dict) else None
        else:
            process_status = process_data.get("process")
            
        if process_status != "ORGANFINISHED":
            return identity_service.formatter.error(
                message="随机金额打款未完成",
                details=transfer_process_result
            )
            
        # 步骤10: 用户实名状态查询
        personal_status_result = await _query_personal_realname_status(
            personal_mobile, environment
        )
        if personal_status_result.get("status") != "success":
            return personal_status_result
            
        # 检查个人实名状态
        personal_status_data = personal_status_result.get("data", {})
        # 处理嵌套的数据结构
        if isinstance(personal_status_data, dict) and "data" in personal_status_data:
            nested_data = personal_status_data.get("data", {})
            personal_realname_status = nested_data.get("realNameStatus") if isinstance(nested_data, dict) else None
        else:
            personal_realname_status = personal_status_data.get("realNameStatus")
            
        if not personal_realname_status:
            return identity_service.formatter.error(
                message="个人实名状态未通过",
                details=personal_status_result
            )
            
        # 步骤11: 企业实名状态查询
        enterprise_status_result = await _query_enterprise_realname_status(
            enterprise_name, environment
        )
        if enterprise_status_result.get("status") != "success":
            return enterprise_status_result
            
        # 检查企业实名状态
        enterprise_status_data = enterprise_status_result.get("data", {})
        # 处理嵌套的数据结构
        if isinstance(enterprise_status_data, dict) and "data" in enterprise_status_data:
            nested_data = enterprise_status_data.get("data", {})
            enterprise_realname_status = nested_data.get("realNameStatus") if isinstance(nested_data, dict) else None
        else:
            enterprise_realname_status = enterprise_status_data.get("realNameStatus")
            
        if not enterprise_realname_status:
            return identity_service.formatter.error(
                message="企业实名状态未通过",
                details=enterprise_status_result
            )
            
        # 返回完整的个人和企业信息
        return identity_service.formatter.success(
            message="个人和企业实名认证完成",
            data={
                "personal": {
                    "accountId": personal_account_id,
                    "name": personal_name,
                    "mobile": personal_mobile,
                    "idcard": personal_idcard
                },
                "enterprise": {
                    "orgId": org_id,
                    "name": enterprise_name,
                    "legalName": legal_name,
                    "legalIdno": legal_idno,
                    "usccCode": uscc_code
                }
            }
        )
        
    except Exception as e:
        logger.error(f"完成个人和企业实名认证全流程异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"完成个人和企业实名认证全流程异常: {str(e)}",
            details={"environment": environment}
        )





async def _get_test_account(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取测试账号信息
    
    Args:
        environment: 环境描述，支持自然语言
        
    Returns:
        测试账号信息
    """
    try:
        logger.info(f"调用获取测试账号接口，入参 environment: {environment}")
        from mcpService.domains.certificate_service import get_test_account
        result = await get_test_account(environment)
        logger.info(f"获取测试账号接口返回结果: {json.dumps(result, ensure_ascii=False)}")
        return result
    except Exception as e:
        logger.error(f"获取测试账号异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"获取测试账号异常: {str(e)}",
            details={"environment": environment}
        )


async def _create_individual_account(
    name: str,
    idcard: str,
    mobile: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建标准签个人账号
    
    Args:
        name: 姓名（测试环境需要以"测试"二字开头）
        idcard: 身份证号
        mobile: 手机号
        environment: 环境描述，支持自然语言
        
    Returns:
        创建的账号信息，包括accountId
    """
    try:
        # 构建请求数据
        request_data = {
            "idcards": {
                "mobile": mobile
            },
            "properties": {
                "name": name
            },
            "credentials": {
                "idno": idcard
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "filter-result": "true"
        }
        
        result = await identity_service.make_api_request(
            path="/v1/accounts/createOverall",
            data=request_data,
            headers=headers,
            method="POST",
            environment=environment,
            service="identity",
            operation="创建标准签个人账号"
        )
        
        # 检查API调用是否成功
        if result.get("status") == "error":
            return result
            
        # 从响应中提取accountId并构建正确的响应格式
        api_data = result.get("data", {})
        if isinstance(api_data, dict) and api_data.get("code") == 0:
            # 成功响应，提取accountId并返回正确格式
            account_data = api_data.get("data", {})
            if isinstance(account_data, dict) and "accountId" in account_data:
                return identity_service.formatter.success(
                    data={
                        "accountId": account_data["accountId"]
                    },
                    message="创建标准签个人账号成功"
                )
        
        # 如果没有正确提取到accountId，返回错误
        return identity_service.formatter.error(
            message="创建标准签个人账号失败，未返回accountId",
            details=result
        )
        
    except Exception as e:
        logger.error(f"创建标准签个人账号异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"创建标准签个人账号异常: {str(e)}",
            details={"name": name, "mobile": mobile}
        )


async def _initiate_telecom_verification(
    account_id: str,
    mobile: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    发起运营商三要素实名认证
    
    Args:
        account_id: 账号ID（来自创建账号接口的返回值）
        mobile: 手机号
        environment: 环境描述，支持自然语言
        
    Returns:
        认证任务信息
    """
    try:
        # 添加调试信息
        logger.info(f"步骤3 - 发起运营商三要素实名认证，入参: account_id={account_id}, mobile={mobile}, environment={environment}")
        
        # 构建请求数据
        request_data = {
            "mobileNo": mobile,
            "notifyUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/api/individual/{account_id}/telecom3Factors"
        
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            headers=headers,
            method="POST",
            environment=environment,
            service="identity",
            operation="发起运营商三要素实名认证"
        )
        
        # 添加响应调试信息
        logger.info(f"步骤3 - 发起运营商三要素实名认证，响应: {json.dumps(result, ensure_ascii=False)}")
        
        return result
        
    except Exception as e:
        logger.error(f"发起运营商三要素实名认证异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"发起运营商三要素实名认证异常: {str(e)}",
            details={"account_id": account_id, "mobile": mobile}
        )


async def _verify_mobile_code(
    auth_task_id: str,
    authcode: str = "123456",
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    手机号验证码校验（测试环境中默认为123456）
    
    Args:
        auth_task_id: 认证任务ID
        authcode: 验证码（测试环境默认为123456）
        environment: 环境描述，支持自然语言
        
    Returns:
        验证结果
    """
    try:
        # 添加调试信息
        logger.info(f"步骤4 - 验证手机号验证码，入参: auth_task_id={auth_task_id}, authcode={authcode}, environment={environment}")
        
        # 构建请求数据
        request_data = {
            "authcode": authcode
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/pub/individual/{auth_task_id}/telecom3Factors"
        
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            headers=headers,
            method="PUT",
            environment=environment,
            service="identity",
            operation="手机号验证码校验"
        )
        
        # 添加响应调试信息
        logger.info(f"步骤4 - 验证手机号验证码，响应: {json.dumps(result, ensure_ascii=False)}")
        
        return result
        
    except Exception as e:
        logger.error(f"手机号验证码校验异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"手机号验证码校验异常: {str(e)}",
            details={"auth_task_id": auth_task_id}
        )


async def _create_enterprise_account(
    creator_account_id: str,
    enterprise_name: str,
    legal_name: str,
    legal_idno: str,
    uscc_code: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建标准签企业账号
    
    Args:
        creator_account_id: 创建者个人账号ID
        enterprise_name: 企业名称（需要以"esigntest测试"开头）
        legal_name: 法人姓名
        legal_idno: 法人身份证号
        uscc_code: 统一社会信用代码
        environment: 环境描述，支持自然语言
        
    Returns:
        创建的企业账号信息
    """
    try:
        # 构建请求数据
        request_data = {
            "creater": creator_account_id,
            "properties": {
                "name": enterprise_name,
                "legalName": legal_name,
                "legalIdno": legal_idno
            },
            "credentials": {
                "usccCode": uscc_code
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "filter-result": "true"
        }
        
        result = await identity_service.make_api_request(
            path="/v1/organizations/create",
            data=request_data,
            headers=headers,
            method="POST",
            environment=environment,
            service="identity",
            operation="创建标准签企业账号"
        )
        
        # 检查API调用是否成功
        if result.get("status") == "error":
            return result
            
        # 从响应中提取orgId并构建正确的响应格式
        api_data = result.get("data", {})
        if isinstance(api_data, dict) and api_data.get("code") == 0:
            # 成功响应，提取orgId并返回正确格式
            account_data = api_data.get("data", {})
            if isinstance(account_data, dict) and "orgId" in account_data:
                return identity_service.formatter.success(
                    data={
                        "orgId": account_data["orgId"]
                    },
                    message="创建标准签企业账号成功"
                )
        
        # 如果没有正确提取到orgId，返回错误
        return identity_service.formatter.error(
            message="创建标准签企业账号失败，未返回orgId",
            details=result
        )
        
    except Exception as e:
        logger.error(f"创建标准签企业账号异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"创建标准签企业账号异常: {str(e)}",
            details={
                "creator_account_id": creator_account_id,
                "enterprise_name": enterprise_name
            }
        )


async def _initiate_enterprise_verification(
    org_id: str,
    agent_account_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    发起三要素企业实名认证
    
    Args:
        org_id: 企业组织ID
        agent_account_id: 代理人个人账号ID
        environment: 环境描述，支持自然语言
        
    Returns:
        企业实名认证信息
    """
    try:
        # 构建请求数据
        request_data = {
            "agentAccountId": agent_account_id
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/api/organization/{org_id}/threeFactors"
        
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            headers=headers,
            method="POST",
            environment=environment,
            service="identity",
            operation="发起三要素企业实名认证"
        )
        
        # 检查API调用是否成功
        if result.get("status") == "error":
            return result
            
        # 从响应中提取flowId并构建正确的响应格式
        api_data = result.get("data", {})
        if isinstance(api_data, dict) and api_data.get("code") == 0:
            # 成功响应，提取flowId并返回正确格式
            account_data = api_data.get("data", {})
            if isinstance(account_data, dict) and "flowId" in account_data:
                return identity_service.formatter.success(
                    data={
                        "flowId": account_data["flowId"]
                    },
                    message="发起三要素企业实名认证成功"
                )
        
        # 如果没有正确提取到flowId，返回错误
        return identity_service.formatter.error(
            message="发起三要素企业实名认证失败，未返回flowId",
            details=result
        )
        
    except Exception as e:
        logger.error(f"发起三要素企业实名认证异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"发起三要素企业实名认证异常: {str(e)}",
            details={"org_id": org_id, "agent_account_id": agent_account_id}
        )


async def _initiate_transfer_payment(
    flow_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    发起随机金额打款
    
    Args:
        flow_id: 实名认证流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        打款结果
    """
    try:
        # 构建请求数据
        request_data = {
            "province": "浙江",
            "subbranch": "xx银行杭州高新支行",
            "bank": "xx银行",
            "city": "杭州市",
            "cnapsCode": "307***2584",
            "cardNo": "110034**75702",
            "contextId": "f0a7927dxxxx1130d86c8fa8",
            "notifyUrl": "http://172.xx.xx.10:8080/testnotify/msgRecive"
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Service-Id": "footstone-identity",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/pub/organization/{flow_id}/transferRandomAmount"
        
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            headers=headers,
            method="PUT",
            environment=environment,
            service="identity",
            operation="发起随机金额打款"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"发起随机金额打款异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"发起随机金额打款异常: {str(e)}",
            details={"flow_id": flow_id}
        )


async def _verify_random_amount(
    flow_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    随机金额校验
    
    Args:
        flow_id: 实名认证流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        校验结果
    """
    try:
        # 构建请求数据
        request_data = {
            "amount": 0.01
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Service-Id": "footstone-identity",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/pub/organization/{flow_id}/verifyRandomAmount"
        
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            headers=headers,
            method="PUT",
            environment=environment,
            service="identity",
            operation="随机金额校验"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"随机金额校验异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"随机金额校验异常: {str(e)}",
            details={"flow_id": flow_id}
        )


async def _query_transfer_process(
    flow_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询随机金额打款进度
    
    Args:
        flow_id: 实名认证流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        查询结果
    """
    try:
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Service-Id": "footstone-identity",
            "filter-result": "true"
        }

        path = f"/v2/identity/auth/pub/organization/{flow_id}/transferProcess"
        
        result = await identity_service.make_api_request(
            path=path,
            headers=headers,
            method="GET",
            environment=environment,
            service="identity",
            operation="查询随机金额打款进度"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"查询随机金额打款进度异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"查询随机金额打款进度异常: {str(e)}",
            details={"flow_id": flow_id}
        )


async def _query_personal_realname_status(
    mobile: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    用户实名状态查询
    
    Args:
        mobile: 用户手机号
        environment: 环境描述，支持自然语言
        
    Returns:
        查询结果
    """
    try:
        # 构建请求参数
        request_data = {
            "email": "",
            "mobile": mobile
        }
        
        path = "/v2/open/accounts/realNameInfo"
        
        # 根据环境确定正确的服务域名
        if environment and ("测试" in environment or "test" in environment.lower()):
            service = "http://footstone-user-api.testk8s.tsign.cn"
        elif environment and ("模拟" in environment or "sml" in environment.lower()):
            service = "http://footstone-user-api.smlk8s.esign.cn"
        else:
            service = "user"  # 默认服务
            
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            method="POST",
            environment=environment,
            service=service,
            operation="用户实名状态查询"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"用户实名状态查询异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"用户实名状态查询异常: {str(e)}",
            details={"mobile": mobile}
        )


async def _add_single_user_channel(
    enterprise_name: str,
    cert_no: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    添加单用户通道配置（仅在模拟环境中使用）
    
    Args:
        enterprise_name: 企业名称
        cert_no: 统一社会信用代码
        environment: 环境描述，支持自然语言
        
    Returns:
        配置结果
    """
    try:
        # 计算两年后的毫秒级时间戳
        future_date = datetime.now() + timedelta(days=365*2)
        lost_time = int(future_date.timestamp() * 1000)
        
        # 构建请求数据
        request_data = {
            "name": enterprise_name,
            "certNo": cert_no,
            "verifyType": 1,
            "provider": "TEST",
            "lostTime": str(lost_time)
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        # 使用完整的URL直接调用RPC接口
        url = "http://infoauth.smlk8s.esign.cn/addRouteConfig/request"
        
        result = await identity_service.make_api_request(
            url=url,
            data=request_data,
            headers=headers,
            method="POST",
            environment=environment,
            service="identity",
            operation="添加单用户通道配置"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"添加单用户通道配置异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"添加单用户通道配置异常: {str(e)}",
            details={"enterprise_name": enterprise_name, "cert_no": cert_no}
        )


async def _query_enterprise_realname_status(
    enterprise_name: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    企业实名状态查询
    
    Args:
        enterprise_name: 企业名称
        environment: 环境描述，支持自然语言
        
    Returns:
        查询结果
    """
    try:
        # 构建请求参数
        request_data = {
            "name": enterprise_name
        }
        
        path = "/v2/open/organizations/realNameInfo"
        
        # 根据环境确定正确的服务域名
        if environment and ("测试" in environment or "test" in environment.lower()):
            service = "http://footstone-user-api.testk8s.tsign.cn"
        elif environment and ("模拟" in environment or "sml" in environment.lower()):
            service = "http://footstone-user-api.smlk8s.esign.cn"
        else:
            service = "user"  # 默认服务
            
        result = await identity_service.make_api_request(
            path=path,
            data=request_data,
            method="POST",
            environment=environment,
            service=service,
            operation="企业实名状态查询"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"企业实名状态查询异常: {str(e)}")
        return identity_service.formatter.error(
            message=f"企业实名状态查询异常: {str(e)}",
            details={"enterprise_name": enterprise_name}
        )
