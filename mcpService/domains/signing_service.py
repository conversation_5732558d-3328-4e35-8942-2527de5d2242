#!/usr/bin/env python3
"""
签署域服务 - 电子签名相关功能实现
提供合同签署、签署流程管理等功能
"""
import copy
import json
import logging
import random
from typing import Dict, Any, Optional, List

import requests

from mcpService.common.OpenApiFactory import OpenApiFactory
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class SigningService(BaseService):
    """签署服务类"""

    def __init__(self):
        super().__init__(domain="signing")


# 全局签署服务实例
signing_service = SigningService()


def get_signers(environment: str = "测试环境", signer_list: List[Dict[str, object]] = None, file_id=None) -> \
        list[Dict[str, Any]]:
    """
    构建签署人信息

    Args:
        environment: 环境描述
        signer_list: 签署方列表

    Returns:
        签署人配置数据
    """
    if signer_list is None:
        signer_list = [{"签署人类型": "个人", "签署区类型": "普通", "顺序": 1}]

    data_provider = OpenApiFactory.create(environment)

    signer_template = {
        "orgSignerInfo": {
            "orgName": data_provider.default_org_name,
            "transactorInfo": {
                "psnAccount": data_provider.default_phone
            }
        },
        "psnSignerInfo": {
            "psnAccount": data_provider.default_phone
        },
        "signConfig": {
            "forcedReadingTime": 0,
            "signOrder": 1,
            "signTaskType": 0
        },
        "signFields": [
            {
                "customBizNum": "",
                "fileId": file_id,
                "normalSignFieldConfig": {
                    "autoSign": False,
                    "freeMode": False,
                    "movableSignField": True,
                    "psnSealStyles": "",
                    "signFieldPosition": {
                        "acrossPageMode": "ALL",
                        "acrossPageOffset": 0,
                        "positionPage": "1",
                        "positionX": 1,
                        "positionY": 1
                    },
                    "signFieldStyle": 1
                },
                "remarkSignFieldConfig": {
                    "aiCheck": 0,
                    "freeMode": False,
                    "inputType": 1,
                    "movableSignField": True,
                    "remarkContent": "1234",
                    "remarkFontSize1": 20,
                    "signFieldHeight": 100,
                    "signFieldPosition": {
                        "acrossPageMode": "",
                        "acrossPageOffset": 0,
                        "positionPage": "1",
                        "positionX": 1,
                        "positionY": 1
                    },
                    "signFieldWidth": 400
                },
                "signFieldType": 0
            }
        ],
        "signerType": 0
    }

    signers = []
    for i, signer_info in enumerate(signer_list, start=1):
        sign_temp = copy.deepcopy(signer_template)

        # 设置默认值
        if '签署人类型' not in signer_info:
            signer_info['签署人类型'] = '个人'
        if '签署区类型' not in signer_info:
            signer_info['签署区类型'] = '普通'
        if '签署区样式' not in signer_info:
            signer_info['签署区样式'] = '单页'
        if '手机号' not in signer_info:
            signer_info['手机号'] = data_provider.default_phone
        if '企业名称' not in signer_info:
            signer_info['企业名称'] = data_provider.default_org_name
        if '顺序' not in signer_info:
            signer_info['顺序'] = 1
        else:
            sign_temp['signConfig']['signOrder'] = signer_info['顺序']

        # 设置签署人类型
        if '个人' in signer_info['签署人类型']:
            del sign_temp["orgSignerInfo"]
            sign_temp['signerType'] = 0
            sign_temp['psnSignerInfo']['psnAccount'] = signer_info['手机号']
        elif '企业' in signer_info['签署人类型']:
            del sign_temp["psnSignerInfo"]
            sign_temp['signerType'] = 1
            sign_temp['orgSignerInfo']['orgName'] = signer_info['企业名称']
            sign_temp['orgSignerInfo']['transactorInfo']['psnAccount'] = signer_info['手机号']
        elif '法人' in signer_info['签署人类型']:
            del sign_temp["psnSignerInfo"]
            sign_temp['signerType'] = 2
            sign_temp['orgSignerInfo']['orgName'] = signer_info['企业名称']
            sign_temp['orgSignerInfo']['transactorInfo']['psnAccount'] = signer_info['手机号']
        elif '经办人' in signer_info['签署人类型']:
            del sign_temp["psnSignerInfo"]
            sign_temp['signerType'] = 3
            sign_temp['orgSignerInfo']['orgName'] = signer_info['企业名称']
            sign_temp['orgSignerInfo']['transactorInfo']['psnAccount'] = signer_info['手机号']

        # 设置签署区类型
        if '备注' in signer_info['签署区类型']:
            del sign_temp["signFields"][0]["normalSignFieldConfig"]
            sign_temp['signFields'][0]['remarkSignFieldConfig']['signFieldPosition']['positionX'] = random.uniform(100,
                                                                                                                   500)
            sign_temp['signFields'][0]['remarkSignFieldConfig']['signFieldPosition']['positionY'] = random.uniform(400,
                                                                                                                   700)
            sign_temp['signFields'][0]['signFieldType'] = 1
        else:
            del sign_temp["signFields"][0]["remarkSignFieldConfig"]
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldPosition']['positionX'] = random.uniform(100,
                                                                                                                   500)
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldPosition']['positionY'] = random.uniform(400,
                                                                                                                   700)

        # 设置签署区样式
        if '自由' in signer_info['签署区样式'] and ('备注' not in signer_info['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['freeMode'] = True
        if '自由' in signer_info['签署区样式'] and ('备注' in signer_info['签署区类型']):
            sign_temp['signFields'][0]['remarkSignFieldConfig']['freeMode'] = True
        if '单页' in signer_info['签署区样式'] and ('备注' not in signer_info['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldStyle'] = 1
        elif '骑缝' in signer_info['签署区样式'] and ('备注' not in signer_info['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldStyle'] = 2

        # 设置签署顺序

        signers.append(sign_temp)

    return signers


async def create_signing_flow(
        flow_name: str,
        document_content: str,
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建签署流程
    
    Args:
        flow_name: 流程名称
        document_content: 文档内容
        environment: 环境描述，支持自然语言
        
    Returns:
        创建结果
    """
    try:
        request_data = {
            "flowName": flow_name,
            "documentContent": document_content
        }

        result = await signing_service.make_api_request(
            path="/signing/flow/create",
            data=request_data,
            environment=environment,
            operation="创建签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"创建签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"创建签署流程异常: {str(e)}",
            details={"flow_name": flow_name}
        )


async def add_signer(
        flow_id: str,
        signer_name: str,
        signer_mobile: str,
        signer_idcard: str,
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    添加签署人
    
    Args:
        flow_id: 流程ID
        signer_name: 签署人姓名
        signer_mobile: 签署人手机号
        signer_idcard: 签署人身份证号
        environment: 环境描述，支持自然语言
        
    Returns:
        添加结果
    """
    try:
        request_data = {
            "flowId": flow_id,
            "signerName": signer_name,
            "signerMobile": signer_mobile,
            "signerIdcard": signer_idcard
        }

        result = await signing_service.make_api_request(
            path="/signing/signer/add",
            data=request_data,
            environment=environment,
            operation="添加签署人"
        )

        return result

    except Exception as e:
        logger.error(f"添加签署人异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"添加签署人异常: {str(e)}",
            details={"flow_id": flow_id, "signer_name": signer_name}
        )


async def start_signing(
        flow_id: str,
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    启动签署流程
    
    Args:
        flow_id: 流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        启动结果
    """
    try:
        request_data = {
            "flowId": flow_id
        }

        result = await signing_service.make_api_request(
            path="/signing/flow/start",
            data=request_data,
            environment=environment,
            operation="启动签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"启动签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"启动签署流程异常: {str(e)}",
            details={"flow_id": flow_id}
        )


async def query_signing_status(
        flow_id: str,
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询签署状态
    
    Args:
        flow_id: 流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        签署状态
    """
    try:
        request_data = {
            "flowId": flow_id
        }

        result = await signing_service.make_api_request(
            path="/signing/flow/status",
            data=request_data,
            environment=environment,
            operation="查询签署状态"
        )

        return result

    except Exception as e:
        logger.error(f"查询签署状态异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"查询签署状态异常: {str(e)}",
            details={"flow_id": flow_id}
        )


async def cancel_signing(
        flow_id: str,
        reason: str = "测试取消",
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    取消签署流程

    Args:
        flow_id: 流程ID
        reason: 取消原因
        environment: 环境描述，支持自然语言

    Returns:
        取消结果
    """
    try:
        request_data = {
            "flowId": flow_id,
            "reason": reason
        }

        result = await signing_service.make_api_request(
            path="/signing/flow/cancel",
            data=request_data,
            environment=environment,
            operation="取消签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"取消签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"取消签署流程异常: {str(e)}",
            details={"flow_id": flow_id, "reason": reason}
        )


async def create_flow_one_step(
        file_id: str,
        file_name: str,
        signer_account_id: str,
        authorized_account_id: str,
        business_scene: str = "一步创建流程",
        pos_page: str = "1",
        pos_x: int = 100,
        pos_y: int = 500,
        sign_type: int = 1,
        third_order_no: str = "mcp_test",
        app_id: Optional[str] = None,
        environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    一步创建签署流程

    Args:
        file_id: 文档文件ID
        file_name: 文档文件名
        signer_account_id: 签署人账号ID
        authorized_account_id: 授权账号ID
        business_scene: 业务场景描述
        pos_page: 签署位置页码
        pos_x: 签署位置X坐标
        pos_y: 签署位置Y坐标
        sign_type: 签署类型 (1=签名)
        third_order_no: 第三方订单号
        app_id: 应用ID，不传则使用环境默认值
        environment: 环境描述，支持自然语言

    Returns:
        创建结果
    """
    try:
        # 构建完整的请求数据，按照curl中的结构
        request_data = {
            "docs": [
                {
                    "encryption": 0,
                    "fileId": file_id,
                    "fileName": file_name,
                    "source": 0
                }
            ],
            "flowInfo": {
                "autoArchive": True,
                "autoInitiate": True,
                "businessScene": business_scene,
                "flowConfigInfo": {
                    "notifyConfig": {
                        "noticeDeveloperUrl": "https://libaohui.com.cn/callback/ding",
                        "noticeType": "1,2,3,4"
                    },
                    "signConfig": {
                        "redirectUrl": "",
                        "signPlatform": "1,2,3,5"
                    }
                },
                "hashSign": False,
                "remark": "MCP工具创建"
            },
            "signers": [
                {
                    "signOrder": 1,
                    "signerAccount": {
                        "signerAccountId": signer_account_id,
                        "authorizedAccountId": authorized_account_id
                    },
                    "signfields": [
                        {
                            "autoExecute": False,
                            "actorIndentityType": 2,
                            "certId": "",
                            "fileId": file_id,
                            "sealType": "",
                            "posBean": {
                                "posPage": pos_page,
                                "posX": pos_x,
                                "posY": pos_y
                            },
                            "signType": sign_type
                        }
                    ],
                    "thirdOrderNo": third_order_no
                }
            ]
        }

        # 构建请求头
        headers = {
            "X-Tsign-Open-Auth-Mode": "simple",
            "Content-Type": "application/json;charset=UTF-8"
        }

        # 如果指定了app_id，添加到请求头
        if app_id:
            headers["X-Tsign-Open-App-Id"] = app_id

        result = await signing_service.make_api_request(
            url="http://in-test-openapi.tsign.cn/api/v3/signflows/createFlowOneStep",
            data=request_data,
            headers=headers,
            environment=environment,
            service="openapi",
            operation="一步创建签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"一步创建签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"一步创建签署流程异常: {str(e)}",
            details={
                "file_id": file_id,
                "file_name": file_name,
                "signer_account_id": signer_account_id,
                "business_scene": business_scene
            }
        )


async def one_click_sign(
        env: str,
        flow_id: str,
        group: str = "DEFAULT",
        sign_type: str = "SIGN"
) -> Dict[str, Any]:
    """
    一键签署

    Args:
        env: 环境类型，如"测试环境"，"模拟环境"
        flow_id: 流程ID，如"fc7095df4d4d4977a006342c1d629aba"
        group: 签署组，默认"DEFAULT"
        sign_type: 枚举值：SIGN（签署），REVOKE（撤回），REFUSE（拒签），EXPIRE（过期）

    Returns:
        签署结果
    """
    try:
        # 构建请求数据
        request_data = {
            "env": env,
            "flowId": flow_id,
            "group": group,
            "type": sign_type
        }

        result = await signing_service.make_api_request(
            url="http://sdk.smlk8s.esign.cn/sign/start",
            data=request_data,
            method="POST",
            service="sign",
            operation="一键签署"
        )

        return result

    except Exception as e:
        logger.error(f"一键签署异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"一键签署异常: {str(e)}",
            details={"env": env, "flow_id": flow_id, "group": group, "type": sign_type}
        )


async def get_sign_url(
        env: str = "测试环境",
        signer_list: Optional[List[Dict[str, str]]] = None,
        group: str = "default",
        FDA: bool = False,
        发起方: str = '平台',
        文件: str = None
) -> Dict[str, Any]:
    """
    获取签署链接

    Args:
        env: 环境描述，支持自然语言，如"测试环境"、"模拟环境"
        signer_list: 签署方列表，每个元素包含签署人类型、签署区类型等信息
        group: 签署组，默认"default"
        FDA: FDA签署，默认为False
    Returns:
        包含签署链接的结果
    """
    try:
        if signer_list is None:
            signer_list = [{"签署人类型": "个人", "签署区类型": "普通"}]

        data_provider = OpenApiFactory.create(env)

        # 构建请求头
        headers = {
            'X-Tsign-Open-App-Id': data_provider.default_app_id,
            'X-Tsign-Open-Auth-Mode': 'simple',
            'Content-Type': 'application/json;charset=UTF-8',
            'X-Tsign-Service-Group': group
        }
        if 文件 is None:
            文件 = data_provider.default_file_id
        signers = get_signers(environment=env, signer_list=signer_list, file_id=文件)

        # 构建签署流程创建请求数据
        request_data = {
            "docs": [{"fileId": 文件}],
            "signFlowConfig": {
                "autoFinish": True,
                "autoStart": True,
                "identityVerify": False,
                "signFlowTitle": "MCP发起的流程"
            },
            "signFlowInitiator": {
                "orgInitiator": {
                    "orgId": data_provider.default_org_id,
                    "transactor": {
                        "psnId": data_provider.default_psn_id
                    }
                },
                "psnInitiator": {
                    "psnId": data_provider.default_psn_id
                }
            },
            "signers": signers
        }

        if 发起方 is None or '平台' in 发起方:
            del request_data["signFlowInitiator"]
        elif '企业' in 发起方:
            del request_data["signFlowInitiator"]['psnInitiator']
        elif '个人' in 发起方:
            del request_data["signFlowInitiator"]['orgInitiator']
        else:
            del request_data["signFlowInitiator"]

        if FDA:
            sign_config = request_data["signFlowConfig"].setdefault('signConfig', {})
            sign_config['signType'] = 'FDAType'

        # 创建签署流程
        create_flow_result = requests.post(
            url=data_provider.open_api_url + "/v3/sign-flow/create-by-file",
            data=json.dumps(request_data),
            headers=headers
        ).json()

        logger.info(f"请求参数为：{json.dumps(request_data)}")
        logger.info(f"返回结果为：{json.dumps(create_flow_result)}")

        # 获取流程ID

        if create_flow_result['code'] != 0:
            return signing_service.formatter.error(
                message="创建签署流程失败！",
                details=create_flow_result
            )

        flow_id = create_flow_result['data']['signFlowId']
        logger.info(f"获取的flowId为：{flow_id}")
        if request_data.get("signers", [{}])[0].get("psnSignerInfo", {}).get("psnAccount") is not None:
            psnAccount = request_data.get("signers")[0].get("psnSignerInfo").get("psnAccount")
        else:
            psnAccount = request_data.get("signers")[0].get("orgSignerInfo").get("transactorInfo").get("psnAccount")
        # 获取签署链接
        sign_url_data = {
            "operator": {
                "psnAccount": psnAccount
            },
            "clientType": "ALL",
            "appScheme": "",
            "needLogin": False,
            "urlType": 2
        }

        sign_url_result = requests.post(
            url=data_provider.open_api_url + f"/v3/sign-flow/{flow_id}/sign-url",
            data=json.dumps(sign_url_data),
            headers=headers
        ).json()

        if sign_url_result['code'] != 0:
            return signing_service.formatter.error(
                message="获取链接失败！",
                flowId=flow_id,
                details=sign_url_result
            )

        shortUrl = sign_url_result['data']['shortUrl']
        url = sign_url_result['data']['url']
        logger.info(f"获取签署链接：{shortUrl}")

        return signing_service.formatter.success(
            message="成功获取签署链接",
            data={
                "flowId": flow_id,
                "signUrl": url,
                "shortUrl": shortUrl
            }
        )

    except Exception as e:
        logger.error(f"获取签署链接异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"获取签署链接异常: {str(e)}",
            details={
                "environment": env,
                "signer_list": signer_list,
                "group": group,
                "FDA": FDA
            }
        )
