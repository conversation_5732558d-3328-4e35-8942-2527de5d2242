#!/usr/bin/env python3
"""
提示词服务 - 提示词管理相关功能
提供提示词类型查询、内容获取、搜索等功能
"""
import os
import yaml
import logging
from typing import Dict, Any, List, Optional
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


class PromptService:
    """提示词服务类"""
    
    def __init__(self):
        """初始化提示词服务"""
        self.prompt_dir = "app/testCasePromptAndFiles"
        self.prompt_config_file = os.path.join(self.prompt_dir, "prompt_config.yml")
        self.prompt_types = self._load_prompt_types()
    
    def _load_prompt_types(self) -> Dict[str, Any]:
        """加载提示词类型配置"""
        try:
            if not os.path.exists(self.prompt_config_file):
                logger.warning(f"提示词配置文件不存在: {self.prompt_config_file}")
                return {}
            
            with open(self.prompt_config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return config.get("prompt_types", {})
            
        except Exception as e:
            logger.error(f"加载提示词类型配置失败: {str(e)}")
            return {}
    
    async def get_prompt_types(self) -> Dict[str, Any]:
        """获取所有提示词类型"""
        try:
            prompt_types_list = []
            
            for prompt_type, info in self.prompt_types.items():
                prompt_types_list.append({
                    "type": prompt_type,
                    "description": info.get("description", ""),
                    "file": info.get("file", ""),
                    "reference_file": info.get("reference_file", "")
                })
            
            return formatter.success(
                data={
                    "prompt_types": prompt_types_list,
                    "total": len(prompt_types_list)
                },
                message=f"获取到 {len(prompt_types_list)} 个提示词类型"
            )
            
        except Exception as e:
            logger.error(f"获取提示词类型失败: {str(e)}")
            return formatter.error(
                message=f"获取提示词类型失败: {str(e)}"
            )
    
    async def get_prompt_content(self, prompt_type: str) -> Dict[str, Any]:
        """获取指定类型的提示词内容和参考格式"""
        try:
            if prompt_type not in self.prompt_types:
                return formatter.error(
                    message=f"未找到提示词类型: {prompt_type}"
                )
            
            prompt_info = self.prompt_types[prompt_type]
            
            # 读取提示词文件
            prompt_file_path = os.path.join(self.prompt_dir, prompt_info['file'])
            if not os.path.exists(prompt_file_path):
                return formatter.error(
                    message=f"提示词文件不存在: {prompt_file_path}"
                )
            
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt_content = f.read()
            
            # 读取参考格式文件（如果有）
            reference_content = None
            if 'reference_file' in prompt_info and prompt_info['reference_file']:
                reference_file_path = os.path.join(self.prompt_dir, prompt_info['reference_file'])
                if os.path.exists(reference_file_path):
                    with open(reference_file_path, 'r', encoding='utf-8') as f:
                        reference_content = f.read()
            
            # 构建结果
            result_data = {
                "type": prompt_type,
                "description": prompt_info.get('description', ''),
                "content": prompt_content,
                "reference": reference_content
            }
            
            return formatter.success(
                data=result_data,
                message=f"成功获取提示词和参考格式: {prompt_type}"
            )
            
        except Exception as e:
            logger.error(f"获取提示词内容失败: {str(e)}")
            return formatter.error(
                message=f"获取提示词内容失败: {str(e)}"
            )
    
    async def get_all_prompts(self) -> Dict[str, Any]:
        """获取所有提示词内容"""
        try:
            all_prompts = {}
            
            for prompt_type in self.prompt_types.keys():
                result = await self.get_prompt_content(prompt_type)
                if result.get("status") == "success":
                    all_prompts[prompt_type] = result["data"]
            
            return formatter.success(
                data={
                    "prompts": all_prompts,
                    "total": len(all_prompts)
                },
                message=f"成功获取 {len(all_prompts)} 个提示词"
            )
            
        except Exception as e:
            logger.error(f"获取所有提示词失败: {str(e)}")
            return formatter.error(
                message=f"获取所有提示词失败: {str(e)}"
            )
    
    async def search_prompts(self, keyword: str) -> Dict[str, Any]:
        """搜索提示词"""
        try:
            matching_prompts = {}
            
            for prompt_type, prompt_info in self.prompt_types.items():
                # 在类型名称和描述中搜索
                if (keyword.lower() in prompt_type.lower() or 
                    keyword.lower() in prompt_info.get('description', '').lower()):
                    
                    result = await self.get_prompt_content(prompt_type)
                    if result.get("status") == "success":
                        matching_prompts[prompt_type] = result["data"]
            
            return formatter.success(
                data={
                    "keyword": keyword,
                    "matches": matching_prompts,
                    "total": len(matching_prompts)
                },
                message=f"找到 {len(matching_prompts)} 个匹配的提示词"
            )
            
        except Exception as e:
            logger.error(f"搜索提示词失败: {str(e)}")
            return formatter.error(
                message=f"搜索提示词失败: {str(e)}"
            )


# 全局提示词服务实例
prompt_service = PromptService()


# 导出函数
async def get_prompt_types() -> Dict[str, Any]:
    """获取所有提示词类型"""
    return await prompt_service.get_prompt_types()


async def get_prompt_content(prompt_type: str) -> Dict[str, Any]:
    """获取指定类型的提示词内容"""
    return await prompt_service.get_prompt_content(prompt_type)


async def get_all_prompts() -> Dict[str, Any]:
    """获取所有提示词内容"""
    return await prompt_service.get_all_prompts()


async def search_prompts(keyword: str) -> Dict[str, Any]:
    """搜索提示词"""
    return await prompt_service.search_prompts(keyword)


async def get_接口生成提示词() -> Dict[str, Any]:
    """获取接口生成提示词"""
    return await get_prompt_content("接口生成提示词")


async def get_必填参数提示词() -> Dict[str, Any]:
    """获取必填参数提示词"""
    return await get_prompt_content("必填参数提示词")


async def get_枚举值提示词() -> Dict[str, Any]:
    """获取枚举值提示词"""
    return await get_prompt_content("枚举值提示词")


async def get_必填与枚举合并提示词() -> Dict[str, Any]:
    """获取必填与枚举合并提示词"""
    return await get_prompt_content("必填与枚举合并提示词")


async def get_通用HttpRunner测试用例生成提示词() -> Dict[str, Any]:
    """获取通用HttpRunner测试用例生成提示词"""
    return await get_prompt_content("通用HttpRunner测试用例生成提示词")


async def get_全套集测提示词() -> Dict[str, Any]:
    """获取全套集测提示词"""
    return await get_all_prompts()


async def get_测试用例生成提示词() -> Dict[str, Any]:
    """获取完整集成测试用例生成提示词"""
    return await get_prompt_content("完整集成测试用例生成提示词")


async def get_HttpRunner自动化用例生成提示词() -> Dict[str, Any]:
    """获取HttpRunner自动化用例生成提示词"""
    return await get_prompt_content("HttpRunner自动化测试脚本提示词")


async def get_简版测试用例生成提示词() -> Dict[str, Any]:
    """获取简版测试用例生成提示词"""
    return await get_prompt_content("简版测试用例生成提示词")


async def get_XMind格式优化提示词() -> Dict[str, Any]:
    """获取XMind格式优化提示词"""
    return await get_prompt_content("XMind格式优化提示词")