#!/usr/bin/env python3
"""
TMS平台集成服务 - 简化版
根据实际的TMS平台Java接口实现进行调整
"""
import httpx
import logging
from typing import Dict, Any, Optional
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)

class TmsPlatformService(BaseService):
    """TMS平台集成服务类"""
    
    def __init__(self):
        """初始化TMS平台集成服务"""
        self.base_url = "http://test-case-platform-backend.testk8s.tsign.cn"  # TMS服务地址
        self.timeout = 30
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """统一的请求方法"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/tmsdefender/mcp/{endpoint}",
                    params=params
                )
                response.raise_for_status()
                result = response.json()
                
                if result.get("code") == 0:
                    return result.get("data", {})
                else:
                    logger.error(f"接口调用失败: {result.get('message')}")
                    return {}
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return {}
    
    async def get_version_info_by_version_name(self, version_name: str, creator_alias: Optional[str] = None, creator_name: Optional[str] = None) -> Dict[str, Any]:
        """
        根据版本名称获取版本信息
        对应Java接口: getVersionInfoByVersionName
        """
        params = {"versionName": version_name}
        
        # 添加创建者信息（用于区分重复版本）
        if creator_alias:
            params["creatorAlias"] = creator_alias
        if creator_name:
            params["creatorName"] = creator_name
        
        return await self._make_request("getVersionInfoByVersionName", params)
    
    async def get_upload_params(self, version_name: str, creator_alias: Optional[str] = None, creator_name: Optional[str] = None, 
                        uploader_alias: Optional[str] = None, uploader_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取上传参数
        对应Java接口: getUploadParams
        """
        params = {"versionName": version_name}
        
        # 添加创建者信息（用于区分重复版本）
        if creator_alias:
            params["creatorAlias"] = creator_alias
        if creator_name:
            params["creatorName"] = creator_name
        
        # 添加上传者信息（用于获取上传者ID）
        if uploader_alias:
            params["uploaderAlias"] = uploader_alias
        if uploader_name:
            params["uploaderName"] = uploader_name
        
        return await self._make_request("getUploadParams", params)
    
    async def validate_version_permission(self, version_id: int, user_id: int) -> Dict[str, Any]:
        """
        验证版本权限
        对应Java接口: validateVersionPermission
        """
        params = {"versionId": version_id, "userId": user_id}
        return await self._make_request("validateVersionPermission", params)

    
    async def upload_xmind_to_tms(self, file_path: str, version_id: int, user_id: int) -> Dict[str, Any]:
        """
        上传XMind文件到TMS平台
        简化版本，只包含必要的参数
        """
        try:
            from mcpService.platform.upload_service import UploadService
            result = await UploadService.upload_xmind(file_path, user_id, version_id)
            return result
        except Exception as e:
            logger.error(f"上传XMind文件异常: {str(e)}")
            return {"success": False, "message": f"上传异常: {str(e)}"}


# 全局TMS平台服务实例
tms_platform_service = TmsPlatformService()


# 导出函数 - 简化版本
async def get_version_info_by_version_name(version_name: str, creator_alias: Optional[str] = None, creator_name: Optional[str] = None) -> Dict[str, Any]:
    """根据版本名称获取版本信息"""
    return await tms_platform_service.get_version_info_by_version_name(version_name, creator_alias, creator_name)


async def get_upload_params(version_name: str, creator_alias: Optional[str] = None, creator_name: Optional[str] = None, 
                        uploader_alias: Optional[str] = None, uploader_name: Optional[str] = None) -> Dict[str, Any]:
    """获取上传参数"""
    return await tms_platform_service.get_upload_params(version_name, creator_alias, creator_name, uploader_alias, uploader_name)


async def validate_version_permission(version_id: int, user_id: int) -> Dict[str, Any]:
    """验证版本权限"""
    return await tms_platform_service.validate_version_permission(version_id, user_id)


async def upload_xmind_to_tms(file_path: str, version_id: int, user_id: int) -> Dict[str, Any]:
    """上传XMind文件到TMS平台"""
    return await tms_platform_service.upload_xmind_to_tms(file_path, version_id, user_id)


# 保留原有的函数名以确保兼容性，但调用简化后的方法
async def get_user_info_by_alias_or_name(alias: Optional[str] = None, name: Optional[str] = None) -> Dict[str, Any]:
    """通过花名或姓名获取用户信息"""
    return await tms_platform_service.get_user_info_by_alias_or_name(alias, name)


async def smart_upload_xmind(file_path: str, version_name: str, uploader_info: Dict[str, str]) -> Dict[str, Any]:
    """智能上传XMind文件"""
    # 先获取版本信息
    version_info = await get_version_info_by_version_name(version_name)
    if not version_info:
        return {"success": False, "message": "无法获取版本信息"}
    
    # 获取用户信息
    user_id = None
    if "alias" in uploader_info:
        user_info = await get_user_info_by_alias_or_name(alias=uploader_info["alias"])
        user_id = user_info.get("id") if user_info else None
    elif "name" in uploader_info:
        user_info = await get_user_info_by_alias_or_name(name=uploader_info["name"])
        user_id = user_info.get("id") if user_info else None
    
    if not user_id:
        return {"success": False, "message": "无法获取用户信息"}
    
    # 上传文件
    version_id = version_info.get("versionId")
    return await upload_xmind_to_tms(file_path, version_id, user_id)


async def convert_and_upload_xmind(content: str, version_name: str, 
                             uploader_info: Dict[str, str], 
                             optimize_format: bool = True,
                             title: Optional[str] = None) -> Dict[str, Any]:
    """转换内容为XMind并上传到TMS平台"""
    # 这个功能比较复杂，建议暂时不使用，或者分开处理
    logger.warning("convert_and_upload_xmind功能复杂，建议分开处理转换和上传")
    return {"success": False, "message": "建议分开处理转换和上传"}


async def get_upload_url_with_dynamic_params(version_name: str, uploader_alias: str) -> Dict[str, Any]:
    """获取动态参数的上传URL"""
    logger.warning("get_upload_url_with_dynamic_params功能简化，建议直接使用get_upload_params")
    return await get_upload_params(version_name, None, None, uploader_alias, None)