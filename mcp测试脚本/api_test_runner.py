#!/usr/bin/env python3
"""
API测试运行器 - 自动化测试所有接口
"""
import asyncio
import httpx
import time
import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

class APITestRunner:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results: List[Dict[str, Any]] = []
        
    async def health_check(self) -> Tuple[bool, Dict[str, Any]]:
        """健康检查接口测试"""
        try:
            start_time = time.time()
            response = await self.client.get(f"{self.base_url}/health_check")
            end_time = time.time()
            
            success = response.status_code == 200
            result = {
                "endpoint": "/health_check",
                "method": "GET",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success else {"error": "Failed to parse response"}
            }
            return success, result
        except Exception as e:
            result = {
                "endpoint": "/health_check",
                "method": "GET",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            return False, result
    
    async def test_certificate_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试证书域接口"""
        results = []
        
        try:
            # 获取测试账号
            start_time = time.time()
            response = await self.client.get(f"{self.base_url}/certificate/get_test_account")
            end_time = time.time()
            
            success = response.status_code == 200
            result = {
                "endpoint": "/certificate/get_test_account",
                "method": "GET",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success else {"error": "Failed to parse response"}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/certificate/get_test_account",
                "method": "GET",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_signing_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试签署域接口"""
        results = []
        
        try:
            # 一键签署接口测试（使用模拟参数）
            start_time = time.time()
            response = await self.client.get(
                f"{self.base_url}/signing/one_click_sign?env=test&flow_id=test_flow_id&group=DEFAULT&sign_type=SIGN"
            )
            end_time = time.time()
            
            success = response.status_code in [200, 500]  # 500可能是业务逻辑错误，但仍表示接口可达
            result = {
                "endpoint": "/signing/one_click_sign",
                "method": "GET",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success and response.status_code == 200 else {"message": response.text}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/signing/one_click_sign",
                "method": "GET",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_saas_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试SaaS域接口"""
        results = []
        
        try:
            # 注册测试个人账号接口测试（使用模拟参数）
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/saas/register_test_person_account",
                params={
                    "app_id": "test_app_id",
                    "idNo": "110101199003072117",
                    "mobile": "***********",
                    "name": "张三",
                    "thirdPartyUserId": "test_user_001"
                }
            )
            end_time = time.time()
            
            success = response.status_code in [200, 422, 500]  # 422是参数验证错误，但仍表示接口可达
            result = {
                "endpoint": "/saas/register_test_person_account",
                "method": "POST",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success and response.status_code == 200 else {"message": response.text}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/saas/register_test_person_account",
                "method": "POST",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_fee_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试费用域接口"""
        results = []
        
        try:
            # 充值应用数量接口测试（使用模拟参数）
            start_time = time.time()
            response = await self.client.get(
                f"{self.base_url}/fee/add_app_quota?env=test"
            )
            end_time = time.time()
            
            success = response.status_code in [200, 500]  # 500可能是业务逻辑错误，但仍表示接口可达
            result = {
                "endpoint": "/fee/add_app_quota",
                "method": "GET",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success and response.status_code == 200 else {"message": response.text}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/fee/add_app_quota",
                "method": "GET",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_identity_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试实名域接口"""
        results = []
        
        try:
            # 创建实名认证接口测试（使用模拟参数）
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/identity/create_verification",
                params={
                    "name": "张三",
                    "idcard": "110101199003072117",
                    "mobile": "***********"
                }
            )
            end_time = time.time()
            
            success = response.status_code in [200, 500]  # 500可能是业务逻辑错误，但仍表示接口可达
            result = {
                "endpoint": "/identity/create_verification",
                "method": "POST",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success and response.status_code == 200 else {"message": response.text}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/identity/create_verification",
                "method": "POST",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_intention_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试意愿域接口"""
        results = []
        
        try:
            # 创建意愿验证接口测试（使用模拟参数）
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/intention/create_verification",
                params={
                    "signer_name": "张三",
                    "signer_mobile": "***********",
                    "signer_idcard": "110101199003072117",
                    "document_title": "测试文档"
                }
            )
            end_time = time.time()
            
            success = response.status_code in [200, 500]  # 500可能是业务逻辑错误，但仍表示接口可达
            result = {
                "endpoint": "/intention/create_verification",
                "method": "POST",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success and response.status_code == 200 else {"message": response.text}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/intention/create_verification",
                "method": "POST",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def test_platform_endpoints(self) -> List[Tuple[bool, Dict[str, Any]]]:
        """测试平台功能接口"""
        results = []
        
        try:
            # 获取测试用例生成提示词接口测试
            start_time = time.time()
            response = await self.client.post(f"{self.base_url}/platform/get_testcase_generation_prompt")
            end_time = time.time()
            
            success = response.status_code == 200
            result = {
                "endpoint": "/platform/get_testcase_generation_prompt",
                "method": "POST",
                "status_code": response.status_code,
                "response_time": round((end_time - start_time) * 1000, 2),
                "success": success,
                "response": response.json() if success else {"error": "Failed to parse response"}
            }
            results.append((success, result))
            
        except Exception as e:
            result = {
                "endpoint": "/platform/get_testcase_generation_prompt",
                "method": "POST",
                "status_code": None,
                "response_time": 0,
                "success": False,
                "response": {"error": str(e)}
            }
            results.append((False, result))
            
        return results
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始执行API测试...")
        
        # 1. 健康检查
        print("正在执行健康检查...")
        health_success, health_result = await self.health_check()
        self.test_results.append(health_result)
        
        if not health_success:
            print("健康检查失败，无法继续执行其他测试")
            return self.generate_report()
        
        print("健康检查通过")
        
        # 2. 测试各个域的接口
        test_groups = [
            ("证书域", self.test_certificate_endpoints),
            ("签署域", self.test_signing_endpoints),
            ("SaaS域", self.test_saas_endpoints),
            ("费用域", self.test_fee_endpoints),
            ("实名域", self.test_identity_endpoints),
            ("意愿域", self.test_intention_endpoints),
            ("平台功能", self.test_platform_endpoints)
        ]
        
        for group_name, test_func in test_groups:
            print(f"正在测试{group_name}...")
            try:
                results = await test_func()
                self.test_results.extend([r for _, r in results])
                success_count = sum(1 for success, _ in results if success)
                total_count = len(results)
                print(f"{group_name}测试完成 ({success_count}/{total_count} 成功)")
            except Exception as e:
                print(f"{group_name}测试执行失败: {str(e)}")
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - successful_tests
        
        # 计算平均响应时间
        successful_results = [r for r in self.test_results if r["success"]]
        if successful_results:
            avg_response_time = sum(r["response_time"] for r in successful_results) / len(successful_results)
        else:
            avg_response_time = 0
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": round((successful_tests / total_tests * 100) if total_tests > 0 else 0, 2),
                "average_response_time_ms": round(avg_response_time, 2),
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            "test_details": self.test_results
        }
        
        return report
    
    def print_report(self, report: Dict[str, Any]):
        """打印测试报告"""
        summary = report["test_summary"]
        print("\n" + "="*60)
        print("API测试报告")
        print("="*60)
        print(f"测试时间: {summary['generated_at']}")
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功测试: {summary['successful_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']}%")
        print(f"平均响应时间: {summary['average_response_time_ms']} ms")
        print("="*60)
        
        print("\n详细测试结果:")
        print("-" * 100)
        print(f"{'接口':<30} {'方法':<6} {'状态码':<8} {'响应时间(ms)':<12} {'结果':<8}")
        print("-" * 100)
        
        for result in report["test_details"]:
            status_text = "成功" if result["success"] else "失败"
            print(f"{result['endpoint']:<30} {result['method']:<6} {result['status_code'] or 'N/A':<8} "
                  f"{result['response_time']:<12} {status_text:<8}")
        
        if summary['failed_tests'] > 0:
            print("\n失败的测试详情:")
            print("-" * 60)
            for result in report["test_details"]:
                if not result["success"]:
                    print(f"接口: {result['endpoint']}")
                    print(f"错误信息: {result['response']}")
                    print("-" * 60)

async def main():
    """主函数"""
    # 创建测试运行器
    runner = APITestRunner()
    
    try:
        # 运行所有测试
        report = await runner.run_all_tests()
        
        # 打印报告
        runner.print_report(report)
        
        # 保存报告到文件
        report_file = f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"测试执行过程中发生错误: {str(e)}")
    finally:
        await runner.client.aclose()

if __name__ == "__main__":
    asyncio.run(main())