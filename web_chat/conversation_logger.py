"""
对话日志记录器 - 记录每次问答，方便排查问题
"""
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class ConversationLogger:
    """对话日志记录器"""
    
    def __init__(self, log_dir: str = "logs/conversations"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        # 当前日期的日志文件
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        self.log_file = os.path.join(log_dir, f"chat_{self.current_date}.jsonl")
    
    def _check_date_change(self):
        """检查日期是否变化，需要切换日志文件"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        if current_date != self.current_date:
            self.current_date = current_date
            self.log_file = os.path.join(self.log_dir, f"chat_{self.current_date}.jsonl")
    
    def log_conversation(
        self,
        session_id: str,
        user_message: str,
        ai_response: str,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        success: bool = True,
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        记录一次完整的对话
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            ai_response: AI 回复
            tool_calls: 工具调用列表
            success: 是否成功
            error: 错误信息（如果有）
            metadata: 额外的元数据
        """
        self._check_date_change()
        
        # 构建日志记录
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "unix_time": int(time.time()),
            "session_id": session_id,
            "user_message": user_message,
            "ai_response": ai_response,
            "tool_calls": tool_calls or [],
            "success": success,
            "error": error,
            "metadata": metadata or {}
        }
        
        try:
            # 追加到 JSONL 文件
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
            # 同时记录到标准日志
            if success:
                logger.info(
                    f"[对话记录] 会话:{session_id[:8]}... | "
                    f"用户:{user_message[:50]}... | "
                    f"工具:{len(tool_calls or [])}个"
                )
            else:
                logger.error(
                    f"[对话失败] 会话:{session_id[:8]}... | "
                    f"用户:{user_message[:50]}... | "
                    f"错误:{error}"
                )
        
        except Exception as e:
            logger.error(f"记录对话日志失败: {str(e)}")
    
    def log_tool_call(
        self,
        session_id: str,
        tool_name: str,
        tool_params: Dict[str, Any],
        tool_result: Any,
        success: bool = True,
        error: Optional[str] = None
    ):
        """
        记录单个工具调用
        
        Args:
            session_id: 会话ID
            tool_name: 工具名称
            tool_params: 工具参数
            tool_result: 工具结果
            success: 是否成功
            error: 错误信息（如果有）
        """
        self._check_date_change()
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "unix_time": int(time.time()),
            "type": "tool_call",
            "session_id": session_id,
            "tool_name": tool_name,
            "tool_params": tool_params,
            "tool_result": str(tool_result)[:500] if tool_result else None,  # 限制长度
            "success": success,
            "error": error
        }
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
            logger.info(
                f"[工具调用] {tool_name} | "
                f"参数:{json.dumps(tool_params, ensure_ascii=False)[:100]}... | "
                f"成功:{success}"
            )
        
        except Exception as e:
            logger.error(f"记录工具调用日志失败: {str(e)}")
    
    def get_recent_conversations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的对话记录
        
        Args:
            limit: 返回的记录数量
            
        Returns:
            对话记录列表
        """
        try:
            if not os.path.exists(self.log_file):
                return []
            
            conversations = []
            with open(self.log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        if entry.get("type") != "tool_call":  # 只返回对话记录
                            conversations.append(entry)
                    except json.JSONDecodeError:
                        continue
            
            # 返回最近的 N 条
            return conversations[-limit:]
        
        except Exception as e:
            logger.error(f"读取对话日志失败: {str(e)}")
            return []
    
    def search_conversations(
        self,
        keyword: Optional[str] = None,
        session_id: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        搜索对话记录
        
        Args:
            keyword: 关键词（搜索用户消息和AI回复）
            session_id: 会话ID
            start_time: 开始时间（ISO格式）
            end_time: 结束时间（ISO格式）
            limit: 返回的记录数量
            
        Returns:
            匹配的对话记录列表
        """
        try:
            if not os.path.exists(self.log_file):
                return []
            
            results = []
            with open(self.log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        
                        # 只搜索对话记录
                        if entry.get("type") == "tool_call":
                            continue
                        
                        # 过滤条件
                        if session_id and entry.get("session_id") != session_id:
                            continue
                        
                        if start_time and entry.get("timestamp", "") < start_time:
                            continue
                        
                        if end_time and entry.get("timestamp", "") > end_time:
                            continue
                        
                        if keyword:
                            user_msg = entry.get("user_message", "").lower()
                            ai_resp = entry.get("ai_response", "").lower()
                            if keyword.lower() not in user_msg and keyword.lower() not in ai_resp:
                                continue
                        
                        results.append(entry)
                        
                        if len(results) >= limit:
                            break
                    
                    except json.JSONDecodeError:
                        continue
            
            return results
        
        except Exception as e:
            logger.error(f"搜索对话日志失败: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取对话统计信息
        
        Returns:
            统计信息字典
        """
        try:
            if not os.path.exists(self.log_file):
                return {
                    "total_conversations": 0,
                    "total_tool_calls": 0,
                    "success_rate": 0,
                    "unique_sessions": 0
                }
            
            total_conversations = 0
            total_tool_calls = 0
            success_count = 0
            sessions = set()
            
            with open(self.log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        
                        if entry.get("type") == "tool_call":
                            total_tool_calls += 1
                        else:
                            total_conversations += 1
                            if entry.get("success"):
                                success_count += 1
                            sessions.add(entry.get("session_id"))
                    
                    except json.JSONDecodeError:
                        continue
            
            return {
                "total_conversations": total_conversations,
                "total_tool_calls": total_tool_calls,
                "success_rate": success_count / total_conversations if total_conversations > 0 else 0,
                "unique_sessions": len(sessions)
            }
        
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}


# 全局实例
conversation_logger = ConversationLogger()
