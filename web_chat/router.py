"""
Web Chat 路由 - 处理聊天页面和 API 请求
"""
import json
import logging
import os
from typing import List, Dict, Any

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
from openai import AsyncOpenAI
import httpx

from .smart_context import smart_context
from .conversation_logger import conversation_logger

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/web-chat", tags=["Web Chat"])


def clean_tool_markers(text: str) -> str:
    """
    清理 AI 响应中可能出现的工具调用标记
    DeepSeek 有时会在响应中包含这些特殊标记
    """
    import re
    
    # 移除工具调用标记
    patterns = [
        r'<｜tool▁calls▁begin｜>.*?<｜tool▁calls▁end｜>',
        r'<｜tool▁call▁begin｜>.*?<｜tool▁call▁end｜>',
        r'<｜tool▁sep｜>',
        r'<\|tool_calls_begin\|>.*?<\|tool_calls_end\|>',
        r'<\|tool_call_begin\|>.*?<\|tool_call_end\|>',
        r'<\|tool_sep\|>',
    ]
    
    cleaned_text = text
    for pattern in patterns:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.DOTALL)
    
    # 清理多余的空行
    cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
    
    return cleaned_text.strip()

# DeepSeek 客户端配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# MCP 服务配置
MCP_BASE_URL = os.getenv("MCP_BASE_URL", "http://localhost:8000")


@router.get("", response_class=HTMLResponse, summary="MCP 聊天界面")
async def chat_page():
    """返回 MCP 聊天界面 HTML 页面"""
    try:
        html_path = os.path.join(os.path.dirname(__file__), "templates", "chat.html")
        with open(html_path, "r", encoding="utf-8") as f:
            return HTMLResponse(f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="聊天页面未找到")


@router.post("/api/chat", summary="处理聊天请求")
async def chat_with_mcp(request: Request):
    """
    处理用户聊天请求，通过 DeepSeek 调用 MCP 工具
    """
    try:
        data = await request.json()
        user_message = data.get("message", "")
        session_id = data.get("session_id", "default")  # 获取会话ID
        
        if not user_message:
            return {
                "success": False,
                "message": "消息不能为空",
                "tool_calls": []
            }
        
        logger.info(f"[会话 {session_id}] 收到用户消息: {user_message}")
        
        # 初始化 DeepSeek 客户端
        client = AsyncOpenAI(
            api_key=DEEPSEEK_API_KEY,
            base_url=DEEPSEEK_BASE_URL
        )
        
        # 获取所有可用的 MCP 工具
        tools = await get_mcp_tools()
        logger.info(f"加载了 {len(tools)} 个 MCP 工具")
        
        # 获取智能上下文增强
        context_enhancement = smart_context.get_enhanced_prompt(session_id)
        
        # 构建增强的 System Prompt
        base_prompt = """你是一个智能助手，可以调用各种工具来帮助用户完成任务。

重要规则：
1. 仔细分析用户的问题，提取关键信息作为工具参数
2. 如果工具需要参数，必须从用户消息中提取或推断参数值
3. 只传递用户明确提供的参数，不要询问可选参数
4. 查看工具的 required 字段，只有必需参数才必须提供
5. 对于可选参数，如果用户没有提供，优先使用智能默认值
6. 如果有历史调用记录，可以复用之前的参数值
7. 不要说"让我..."、"我来..."等过渡性话语，直接给出结果

参数提取规则：
- 必需参数（在 required 列表中）：必须从用户消息中提取，或使用智能默认值
- 可选参数（不在 required 列表中）：只有用户明确提供时才传递，或使用智能默认值
- 不要因为缺少可选参数而询问用户

工具结果处理规则：
- 如果工具返回空结果或错误，明确告诉用户
- 对于 Wiki搜索，如果没有找到内容，说"未找到相关内容"
- 不要编造或猜测不存在的信息
- 如果结果很少，如实告知用户
- 直接展示工具返回的结果，不要说"让我获取"、"正在查询"等话
- 如果工具已经返回结果，直接整理并呈现给用户

高频工具特别提示：
1. 一键签署：flow_id 是必需的，env 可以使用最近一次的环境
2. 获取测试账号：environment 可选，可以使用最近的环境或不传
3. Wiki搜索：keyword 从用户消息中提取关键词
   - 如果搜索结果为空或很少，明确告诉用户"未找到相关内容"
   - 不要编造不存在的信息
4. 充值账户余额：env 必需，gid 可选，appId 通常不需要
5. 获取签署链接：大部分参数有默认值，通常只需要 env（或使用默认）

示例：
- 用户说"搜索或签" → 调用 wiki_search，参数 {"keyword": "或签"}
- 用户说"一键签署 fc7095df" → 调用 signing_one_click_sign，参数 {"flow_id": "fc7095df", "env": "使用最近的环境"}
- 用户说"充值账户余额，模拟环境，gid：xxx" → 调用 fee_recharge_quota，参数 {"env": "模拟环境", "gid": "xxx"}
- 用户说"获取测试账号" → 调用 certificate_get_test_account，参数 {} 或使用最近的环境
- 用户说"获取签署链接" → 调用 signing_get_sign_url，参数 {}
- 用户说"帮我在模拟环境获取签署链接，个人发起，手机号为***********的个人签" → 调用 signing_get_sign_url，参数 {"env": "模拟环境", "发起方": "个人", "signer_list": [{"签署人类型": "个人", "手机号": "***********"}]}"""
        
        # 添加上下文增强
        system_content = base_prompt + context_enhancement
        
        # 构建消息
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": user_message
            }
        ]
        
        # 第一次调用 DeepSeek
        response = await client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            tools=tools if tools else None,
            stream=False
        )
        
        assistant_message = response.choices[0].message
        
        # 检查是否有工具调用
        if assistant_message.tool_calls:
            logger.info(f"AI 决定调用 {len(assistant_message.tool_calls)} 个工具")
            
            # 执行所有工具调用
            tool_results = []
            tool_names = []
            
            for tool_call in assistant_message.tool_calls:
                tool_name = tool_call.function.name
                tool_args_str = tool_call.function.arguments
                logger.info(f"AI 返回的原始参数: {tool_args_str}")
                
                tool_args = json.loads(tool_args_str)
                logger.info(f"执行工具: {tool_name}, 参数: {tool_args}")
                tool_names.append(tool_name)
                
                try:
                    result = await execute_mcp_tool(tool_name, tool_args)
                    tool_results.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": json.dumps(result, ensure_ascii=False)
                    })
                    
                    # 记住这次工具调用
                    smart_context.remember_tool_call(session_id, tool_name, tool_args, result)
                    
                except Exception as e:
                    logger.error(f"工具执行失败: {tool_name}, 错误: {str(e)}")
                    tool_results.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": json.dumps({"error": str(e)}, ensure_ascii=False)
                    })
            
            # 将工具调用和结果添加到消息历史
            messages.append({
                "role": "assistant",
                "content": assistant_message.content,
                "tool_calls": [
                    {
                        "id": tc.id,
                        "type": "function",
                        "function": {
                            "name": tc.function.name,
                            "arguments": tc.function.arguments
                        }
                    }
                    for tc in assistant_message.tool_calls
                ]
            })
            messages.extend(tool_results)
            
            # 第二次调用 DeepSeek，让它根据工具结果生成最终回复
            final_response = await client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                stream=False
            )
            
            ai_response = final_response.choices[0].message.content
            
            # 清理可能的工具调用标记
            ai_response = clean_tool_markers(ai_response)
            
            # 记录对话日志
            conversation_logger.log_conversation(
                session_id=session_id,
                user_message=user_message,
                ai_response=ai_response,
                tool_calls=[
                    {
                        "tool": name,
                        "params": json.loads(tc.function.arguments)
                    }
                    for name, tc in zip(tool_names, assistant_message.tool_calls)
                ],
                success=True
            )
            
            return {
                "success": True,
                "message": ai_response,
                "tool_calls": tool_names
            }
        
        # 没有工具调用，直接返回 AI 回复
        ai_response = assistant_message.content or "抱歉，我无法回答这个问题。"
        
        # 清理可能的工具调用标记（DeepSeek 有时会返回这些）
        ai_response = clean_tool_markers(ai_response)
        
        # 记录对话日志
        conversation_logger.log_conversation(
            session_id=session_id,
            user_message=user_message,
            ai_response=ai_response,
            tool_calls=[],
            success=True
        )
        
        return {
            "success": True,
            "message": ai_response,
            "tool_calls": []
        }
        
    except Exception as e:
        logger.error(f"聊天处理失败: {str(e)}", exc_info=True)
        
        # 记录失败的对话
        conversation_logger.log_conversation(
            session_id=session_id,
            user_message=user_message,
            ai_response="",
            tool_calls=[],
            success=False,
            error=str(e)
        )
        
        return {
            "success": False,
            "message": f"处理失败: {str(e)}",
            "tool_calls": []
        }


async def get_mcp_tools() -> List[Dict[str, Any]]:
    """
    从 MCP 服务获取所有可用工具，并转换为 OpenAI 工具格式
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 调用 FastAPI 的 OpenAPI 接口获取所有端点
            response = await client.get(f"{MCP_BASE_URL}/openapi.json")
            openapi_spec = response.json()
            
            tools = []
            paths = openapi_spec.get("paths", {})
            
            for path, methods in paths.items():
                for method, details in methods.items():
                    if method.lower() not in ["get", "post"]:
                        continue
                    
                    # 跳过非业务接口
                    if any(skip in path for skip in ["/docs", "/openapi", "/health", "/mcp", "/web-chat"]):
                        continue
                    
                    # 构建工具定义
                    operation_id = details.get("operationId", "")
                    summary = details.get("summary", "")
                    description = details.get("description", summary)
                    
                    if not operation_id:
                        continue
                    
                    # 初始化参数结构 - 确保始终是有效的 object schema
                    parameters = {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                    
                    has_params = False
                    
                    # 处理请求体参数
                    request_body = details.get("requestBody", {})
                    if request_body:
                        content = request_body.get("content", {})
                        json_schema = content.get("application/json", {}).get("schema", {})
                        if json_schema and isinstance(json_schema, dict):
                            # 处理 $ref 引用
                            if "$ref" in json_schema:
                                # 从 components/schemas 中获取实际的 schema
                                ref_path = json_schema["$ref"].split("/")
                                if len(ref_path) >= 3 and ref_path[-2] == "schemas":
                                    schema_name = ref_path[-1]
                                    actual_schema = openapi_spec.get("components", {}).get("schemas", {}).get(schema_name, {})
                                    if actual_schema:
                                        parameters["properties"] = actual_schema.get("properties", {})
                                        parameters["required"] = actual_schema.get("required", [])
                                        has_params = True
                            # 确保 schema 有 type 字段
                            elif json_schema.get("type") == "object":
                                parameters["properties"] = json_schema.get("properties", {})
                                parameters["required"] = json_schema.get("required", [])
                                has_params = True
                            elif "properties" in json_schema:
                                # 有 properties 但没有 type，补充 type
                                parameters["properties"] = json_schema.get("properties", {})
                                parameters["required"] = json_schema.get("required", [])
                                has_params = True
                    
                    # 处理查询参数和路径参数
                    for param in details.get("parameters", []):
                        param_name = param.get("name")
                        param_schema = param.get("schema", {})
                        param_desc = param.get("description", "")
                        param_type = param_schema.get("type", "string")
                        
                        if param_name:
                            parameters["properties"][param_name] = {
                                "type": param_type,
                                "description": param_desc
                            }
                            
                            if param.get("required", False):
                                if param_name not in parameters["required"]:
                                    parameters["required"].append(param_name)
                            
                            has_params = True
                    
                    # 如果没有任何参数，确保 properties 至少是空对象
                    if not has_params:
                        parameters["properties"] = {}
                        parameters["required"] = []
                    
                    # 验证 parameters 结构
                    if not isinstance(parameters.get("properties"), dict):
                        parameters["properties"] = {}
                    if not isinstance(parameters.get("required"), list):
                        parameters["required"] = []
                    
                    # 确保 type 始终是 "object"
                    parameters["type"] = "object"
                    
                    tool = {
                        "type": "function",
                        "function": {
                            "name": operation_id,
                            "description": description or summary or f"调用 {path}",
                            "parameters": parameters
                        }
                    }
                    
                    # 调试日志：检查工具定义
                    logger.debug(f"工具 {operation_id}: parameters type={parameters.get('type')}, properties count={len(parameters.get('properties', {}))}")
                    
                    tools.append(tool)
            
            logger.info(f"成功加载 {len(tools)} 个工具")
            return tools
            
    except Exception as e:
        logger.error(f"获取 MCP 工具失败: {str(e)}")
        return []


async def execute_mcp_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行 MCP 工具调用
    """
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 根据 operation_id 找到对应的路径和方法
            # 这里简化处理，直接通过 OpenAPI 规范查找
            response = await client.get(f"{MCP_BASE_URL}/openapi.json")
            openapi_spec = response.json()
            
            # 查找对应的端点
            target_path = None
            target_method = None
            
            for path, methods in openapi_spec.get("paths", {}).items():
                for method, details in methods.items():
                    if details.get("operationId") == tool_name:
                        target_path = path
                        target_method = method.lower()
                        break
                if target_path:
                    break
            
            if not target_path:
                raise ValueError(f"未找到工具: {tool_name}")
            
            # 构建完整 URL
            url = f"{MCP_BASE_URL}{target_path}"
            
            # 执行请求
            if target_method == "get":
                result = await client.get(url, params=arguments)
            elif target_method == "post":
                result = await client.post(url, json=arguments)
            else:
                raise ValueError(f"不支持的方法: {target_method}")
            
            return result.json()
            
    except Exception as e:
        logger.error(f"执行工具失败: {tool_name}, 错误: {str(e)}")
        raise


@router.get("/api/tools", summary="获取可用工具列表")
async def get_tools_list():
    """获取所有可用的 MCP 工具列表"""
    try:
        tools = await get_mcp_tools()
        return {
            "success": True,
            "count": len(tools),
            "tools": [
                {
                    "name": tool["function"]["name"],
                    "description": tool["function"]["description"]
                }
                for tool in tools
            ]
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "tools": []
        }


@router.get("/api/conversations/recent", summary="获取最近的对话记录")
async def get_recent_conversations(limit: int = 10):
    """获取最近的对话记录"""
    try:
        conversations = conversation_logger.get_recent_conversations(limit)
        return {
            "success": True,
            "count": len(conversations),
            "conversations": conversations
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "conversations": []
        }


@router.get("/api/conversations/search", summary="搜索对话记录")
async def search_conversations(
    keyword: str = None,
    session_id: str = None,
    limit: int = 50
):
    """搜索对话记录"""
    try:
        conversations = conversation_logger.search_conversations(
            keyword=keyword,
            session_id=session_id,
            limit=limit
        )
        return {
            "success": True,
            "count": len(conversations),
            "conversations": conversations
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "conversations": []
        }


@router.get("/api/conversations/stats", summary="获取对话统计")
async def get_conversation_stats():
    """获取对话统计信息"""
    try:
        stats = conversation_logger.get_statistics()
        return {
            "success": True,
            "stats": stats
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "stats": {}
        }
