"""
智能上下文管理 - 针对高频工具的特别优化
提供会话记忆、参数推断、智能默认值等功能
"""
import json
import logging
import time
from typing import Dict, Any, List, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)


class SmartContextManager:
    """智能上下文管理器"""
    
    def __init__(self):
        # 会话存储：{session_id: session_data}
        self.sessions = defaultdict(lambda: {
            "tool_calls": [],  # 工具调用历史
            "variables": {},   # 变量记忆
            "last_env": None,  # 最后使用的环境
            "last_flow_id": None,  # 最后的流程ID
            "created_at": time.time()
        })
        
        # 高频工具配置
        self.high_frequency_tools = {
            "signing_one_click_sign": {
                "name": "一键签署",
                "smart_params": ["env", "flow_id", "group", "sign_type"],
                "defaults": {"group": "DEFAULT", "sign_type": "SIGN"}
            },
            "signing_one_click_sign_post": {
                "name": "一键签署",
                "smart_params": ["env", "flow_id", "group", "sign_type"],
                "defaults": {"group": "DEFAULT", "sign_type": "SIGN"}
            },
            "certificate_get_test_account": {
                "name": "获取测试账号",
                "smart_params": ["environment"],
                "defaults": {}
            },
            "wiki_search": {
                "name": "Wiki搜索",
                "smart_params": ["keyword"],
                "defaults": {}
            },
            "fee_recharge_quota": {
                "name": "充值账户余额",
                "smart_params": ["env", "name", "gid", "appId"],
                "defaults": {}
            },
            "signing_get_sign_url": {
                "name": "获取签署链接",
                "smart_params": ["env", "signer_list", "group", "FDA", "发起方", "文件"],
                "defaults": {"group": "default", "FDA": False, "发起方": "平台"}
            }
        }
    
    def remember_tool_call(self, session_id: str, tool_name: str, params: Dict[str, Any], result: Any = None):
        """记住工具调用"""
        session = self.sessions[session_id]
        
        # 添加到历史
        session["tool_calls"].append({
            "tool": tool_name,
            "params": params,
            "result": result,
            "timestamp": time.time()
        })
        
        # 只保留最近20次调用
        if len(session["tool_calls"]) > 20:
            session["tool_calls"] = session["tool_calls"][-20:]
        
        # 更新变量记忆
        for key, value in params.items():
            if value:  # 只记住非空值
                session["variables"][key] = value
        
        # 特殊记忆
        if "env" in params or "environment" in params:
            session["last_env"] = params.get("env") or params.get("environment")
        
        if "flow_id" in params:
            session["last_flow_id"] = params["flow_id"]
        
        logger.info(f"记住工具调用: {tool_name}, 参数: {params}")
    
    def get_smart_defaults(self, session_id: str, tool_name: str) -> Dict[str, Any]:
        """获取智能默认值"""
        session = self.sessions[session_id]
        defaults = {}
        
        # 检查是否是高频工具
        if tool_name not in self.high_frequency_tools:
            return defaults
        
        tool_config = self.high_frequency_tools[tool_name]
        smart_params = tool_config["smart_params"]
        
        # 1. 从最近的同类工具调用中获取
        for call in reversed(session["tool_calls"]):
            if call["tool"] == tool_name:
                for param in smart_params:
                    if param in call["params"] and param not in defaults:
                        defaults[param] = call["params"][param]
                break
        
        # 2. 从变量记忆中获取
        for param in smart_params:
            if param not in defaults and param in session["variables"]:
                defaults[param] = session["variables"][param]
        
        # 3. 使用工具的默认值
        for param, value in tool_config["defaults"].items():
            if param not in defaults:
                defaults[param] = value
        
        # 4. 特殊处理
        if tool_name in ["signing_one_click_sign", "signing_one_click_sign_post"]:
            # 一键签署：优先使用最后的环境和流程ID
            if "env" not in defaults and session["last_env"]:
                defaults["env"] = session["last_env"]
            if "flow_id" not in defaults and session["last_flow_id"]:
                defaults["flow_id"] = session["last_flow_id"]
        
        if defaults:
            logger.info(f"智能默认值 [{tool_name}]: {defaults}")
        
        return defaults
    
    def get_enhanced_prompt(self, session_id: str, tool_name: Optional[str] = None) -> str:
        """获取增强的 System Prompt"""
        session = self.sessions[session_id]
        
        # 基础提示
        prompt = ""
        
        # 添加最近的工具调用历史
        recent_calls = session["tool_calls"][-3:]
        if recent_calls:
            prompt += "\n\n最近的工具调用历史：\n"
            for i, call in enumerate(recent_calls, 1):
                prompt += f"{i}. {call['tool']}: {json.dumps(call['params'], ensure_ascii=False)}\n"
        
        # 添加智能默认值
        if tool_name and tool_name in self.high_frequency_tools:
            defaults = self.get_smart_defaults(session_id, tool_name)
            if defaults:
                prompt += f"\n智能默认值（可直接使用）：\n{json.dumps(defaults, ensure_ascii=False, indent=2)}\n"
        
        # 添加变量记忆
        if session["variables"]:
            prompt += "\n记住的变量：\n"
            for key, value in list(session["variables"].items())[-5:]:
                prompt += f"- {key}: {value}\n"
        
        return prompt
    
    def get_tool_specific_hints(self, tool_name: str) -> str:
        """获取工具特定的提示"""
        hints = {
            "signing_one_click_sign": """
一键签署工具提示：
- env: 必需，环境类型（如"测试环境"、"模拟环境"）
- flow_id: 必需，流程ID（32位字符串）
- group: 可选，默认"DEFAULT"，通常不需要改
- sign_type: 可选，默认"SIGN"，通常不需要改

参数提取规则：
- 如果用户说"一键签署 xxx"，xxx 就是 flow_id
- 如果用户说"测试环境"，env 就是"测试环境"
- 如果用户没说环境，使用最近一次的环境
- group 和 sign_type 使用默认值即可

示例：
用户："一键签署 fc7095df4d4d4977a006342c1d629aba，测试环境"
→ {"env": "测试环境", "flow_id": "fc7095df4d4d4977a006342c1d629aba"}
""",
            "signing_one_click_sign_post": """
一键签署工具提示：
- env: 必需，环境类型（如"测试环境"、"模拟环境"）
- flow_id: 必需，流程ID（32位字符串）
- group: 可选，默认"DEFAULT"，通常不需要改
- sign_type: 可选，默认"SIGN"，通常不需要改

参数提取规则：
- 如果用户说"一键签署 xxx"，xxx 就是 flow_id
- 如果用户说"测试环境"，env 就是"测试环境"
- 如果用户没说环境，使用最近一次的环境
- group 和 sign_type 使用默认值即可

示例：
用户："一键签署 fc7095df4d4d4977a006342c1d629aba，测试环境"
→ {"env": "测试环境", "flow_id": "fc7095df4d4d4977a006342c1d629aba"}
""",
            "certificate_get_test_account": """
获取测试账号工具提示：
- environment: 可选，环境类型（如"测试环境"、"模拟环境"）

参数提取规则：
- 如果用户说了环境，就传递 environment
- 如果用户没说，可以不传（使用默认环境）
- 或者使用最近一次的环境

示例：
用户："获取测试账号"
→ {} 或 {"environment": "最近使用的环境"}

用户："获取模拟环境的测试账号"
→ {"environment": "模拟环境"}
""",
            "wiki_search": """
Wiki搜索工具提示：
- keyword: 必需，搜索关键词

参数提取规则：
- 从用户消息中提取关键词
- 用户说"搜索 xxx"，xxx 就是 keyword
- 用户说"什么是 xxx"，xxx 就是 keyword

示例：
用户："搜索或签"
→ {"keyword": "或签"}

用户："什么是签署流程"
→ {"keyword": "签署流程"}
""",
            "fee_recharge_quota": """
充值账户余额工具提示：
- env: 必需，环境类型（如"模拟环境"、"测试环境"）
- name: 可选，企业名称
- gid: 可选，企业GID
- appId: 可选，应用ID

参数提取规则：
- env 必须从用户消息中提取
- gid 如果用户提供了就传递
- name 和 appId 通常不需要，使用默认值
- 不要询问可选参数

示例：
用户："充值账户余额，模拟环境，gid：xxx"
→ {"env": "模拟环境", "gid": "xxx"}
""",
            "signing_get_sign_url": """
获取签署链接工具提示：
- env: 可选，环境类型（如"测试环境"、"模拟环境"），默认"测试环境"
- signer_list: 可选，签署方列表，如果用户提到签署人信息就需要构建
- group: 可选，签署组，默认"default"
- FDA: 可选，是否FDA，默认False
- 发起方: 可选，发起方类型，默认"平台"，枚举值：平台、个人、企业
- 文件: 可选，文件ID

参数提取规则：
1. env: 从用户消息中提取环境类型
2. 发起方: 
   - 如果用户说"个人发起"或"个人签署" → "个人"
   - 如果用户说"企业发起" → "企业"
   - 否则使用默认值"平台"
3. signer_list: 如果用户提到签署人信息，需要构建列表
   - 如果用户说"手机号为xxx的个人签" → [{"签署人类型": "个人", "手机号": "xxx"}]
   - 如果用户说"企业xxx签署" → [{"签署人类型": "企业", "企业名称": "xxx"}]
   - 如果没有提到签署人，不传递此参数
4. 其他参数使用默认值

示例：
用户："获取签署链接"
→ {}

用户："获取签署链接，模拟环境"
→ {"env": "模拟环境"}

用户："帮我在模拟环境获取签署链接，个人发起，手机号为13987654321的个人签"
→ {
  "env": "模拟环境",
  "发起方": "个人",
  "signer_list": [{"签署人类型": "个人", "手机号": "13987654321"}]
}

用户："获取签署链接，企业发起，企业名称是测试公司"
→ {
  "发起方": "企业",
  "signer_list": [{"签署人类型": "企业", "企业名称": "测试公司"}]
}
"""
        }
        
        return hints.get(tool_name, "")
    
    def clean_old_sessions(self, max_age_seconds: int = 3600):
        """清理过期的会话"""
        current_time = time.time()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if current_time - session["created_at"] > max_age_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        if expired_sessions:
            logger.info(f"清理了 {len(expired_sessions)} 个过期会话")


# 全局实例
smart_context = SmartContextManager()
