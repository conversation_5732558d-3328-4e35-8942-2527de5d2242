<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Chat - DeepSeek AI 助手</title>
    <!-- Markdown 解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@11.0.0/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            padding: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* Markdown 样式 */
        .message-content h1, .message-content h2, .message-content h3 {
            margin-top: 16px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .message-content h1 { font-size: 20px; }
        .message-content h2 { font-size: 18px; }
        .message-content h3 { font-size: 16px; }
        
        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 24px;
        }
        
        .message-content li {
            margin: 4px 0;
        }
        
        .message-content p {
            margin: 8px 0;
        }
        
        .message-content code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .message-content pre {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
        }
        
        .message-content pre code {
            background: none;
            padding: 0;
        }
        
        .message-content strong {
            font-weight: 600;
            color: #000;
        }
        
        .message-content blockquote {
            border-left: 3px solid #667eea;
            padding-left: 12px;
            margin: 8px 0;
            color: #666;
        }

        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            max-width: 100%;
            text-align: center;
            font-size: 13px;
        }

        .tool-call {
            font-size: 12px;
            margin-top: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            display: inline-block;
        }

        .message.assistant .tool-call {
            background: #e7f3ff;
            color: #0066cc;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        input {
            flex: 1;
            padding: 14px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 24px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        input:focus {
            border-color: #667eea;
        }

        button {
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        button:active:not(:disabled) {
            transform: translateY(0);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 12px;
        }

        .loading.active {
            display: block;
        }

        .loading-dots {
            display: inline-block;
        }

        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            margin: 0 3px;
            animation: bounce 1.4s infinite ease-in-out both;
        }

        .loading-dots span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
            }
            40% {
                transform: scale(1);
            }
        }

        .tools-info {
            padding: 12px;
            background: #e7f3ff;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 13px;
            color: #0066cc;
        }

        /* 滚动条样式 */
        .messages::-webkit-scrollbar {
            width: 8px;
        }

        .messages::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 提示词示例面板 */
        .examples-panel {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .examples-panel.hidden {
            transform: translateX(360px) translateY(-50%);
        }

        .examples-toggle {
            position: fixed;
            right: 20px;
            top: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            font-size: 20px;
            z-index: 1001;
            transition: transform 0.2s;
        }

        .examples-toggle:hover {
            transform: scale(1.1);
        }

        .examples-panel h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }

        .example-category {
            margin-bottom: 20px;
        }

        .example-category h4 {
            font-size: 14px;
            color: #667eea;
            margin: 0 0 8px 0;
            font-weight: 600;
        }

        .example-item {
            background: #f8f9fa;
            padding: 10px 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 3px solid #667eea;
        }

        .example-item:hover {
            background: #e7f3ff;
            transform: translateX(4px);
        }

        .example-text {
            font-size: 13px;
            color: #333;
            margin-bottom: 4px;
        }

        .example-desc {
            font-size: 11px;
            color: #666;
        }

        .examples-panel::-webkit-scrollbar {
            width: 6px;
        }

        .examples-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .examples-panel::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        /* 响应式：小屏幕隐藏示例面板 */
        @media (max-width: 1400px) {
            .examples-panel {
                display: none;
            }
            .examples-toggle {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 提示词示例切换按钮 -->
    <button class="examples-toggle" onclick="toggleExamples()" title="提示词示例">
        💡
    </button>

    <!-- 提示词示例面板 -->
    <div class="examples-panel" id="examplesPanel">
        <h3>💡 提示词示例</h3>
        
        <div class="example-category">
            <h4>🔍 Wiki搜索</h4>
            <div class="example-item" onclick="useExample('搜索或签')">
                <div class="example-text">搜索或签</div>
                <div class="example-desc">搜索Wiki知识库</div>
            </div>
            <div class="example-item" onclick="useExample('什么是签署流程')">
                <div class="example-text">什么是签署流程</div>
                <div class="example-desc">查询概念</div>
            </div>
            <div class="example-item" onclick="useExample('天印产品的UKey需求')">
                <div class="example-text">天印产品的UKey需求</div>
                <div class="example-desc">查询产品需求</div>
            </div>
        </div>

        <div class="example-category">
            <h4>💰 充值账户余额</h4>
            <div class="example-item" onclick="useExample('充值账户余额，模拟环境，gid：daa17177a0464ccc9bca9dc712ac4aee')">
                <div class="example-text">充值账户余额</div>
                <div class="example-desc">指定环境和GID</div>
            </div>
            <div class="example-item" onclick="useExample('充值账户余额，测试环境')">
                <div class="example-text">充值账户余额，测试环境</div>
                <div class="example-desc">使用默认GID</div>
            </div>
        </div>

        <div class="example-category">
            <h4>⚡ 一键签署</h4>
            <div class="example-item" onclick="useExample('一键签署 fc7095df4d4d4977a006342c1d629aba，测试环境')">
                <div class="example-text">一键签署 fc7095df，测试环境</div>
                <div class="example-desc">对指定流程进行签署</div>
            </div>
            <div class="example-item" onclick="useExample('一键签署 abc123def')">
                <div class="example-text">一键签署 abc123def</div>
                <div class="example-desc">使用最近的环境</div>
            </div>
        </div>

        <div class="example-category">
            <h4>📱 获取测试账号</h4>
            <div class="example-item" onclick="useExample('获取测试账号')">
                <div class="example-text">获取测试账号</div>
                <div class="example-desc">获取默认环境的账号</div>
            </div>
            <div class="example-item" onclick="useExample('获取模拟环境的测试账号')">
                <div class="example-text">获取模拟环境的测试账号</div>
                <div class="example-desc">指定环境</div>
            </div>
        </div>

        <div class="example-category">
            <h4>🔗 获取签署链接</h4>
            <div class="example-item" onclick="useExample('获取签署链接')">
                <div class="example-text">获取签署链接</div>
                <div class="example-desc">使用默认配置</div>
            </div>
            <div class="example-item" onclick="useExample('帮我在模拟环境获取签署链接，个人发起，手机号为13987654321的个人签')">
                <div class="example-text">个人发起签署链接</div>
                <div class="example-desc">指定环境和签署人</div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="header">
            <h1>🤖 MCP Chat - DeepSeek AI 助手</h1>
            <p>通过 AI 智能调用 MCP 工具，帮你完成各种任务</p>
        </div>
        
        <div class="messages" id="messages">
            <div class="message system">
                <div class="message-content">
                    👋 你好！我是 DeepSeek AI 助手，可以帮你调用各种 MCP 工具。试试问我一些问题吧！
                </div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-box">
                <input 
                    type="text" 
                    id="input" 
                    placeholder="输入你的问题，例如：查询签署流程、获取文档信息..." 
                    autocomplete="off"
                />
                <button id="sendBtn" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        const messagesDiv = document.getElementById('messages');
        const input = document.getElementById('input');
        const sendBtn = document.getElementById('sendBtn');
        const loading = document.getElementById('loading');

        // 生成或获取会话ID
        let sessionId = localStorage.getItem('mcp_session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('mcp_session_id', sessionId);
        }
        console.log('会话ID:', sessionId);

        // 回车发送
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载时获取可用工具
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/web-chat/api/tools');
                const data = await response.json();
                
                if (data.success && data.count > 0) {
                    addSystemMessage(`✅ 已加载 ${data.count} 个可用工具`);
                }
            } catch (error) {
                console.error('获取工具列表失败:', error);
            }
        });

        async function sendMessage() {
            const message = input.value.trim();
            if (!message) return;

            // 禁用输入
            input.disabled = true;
            sendBtn.disabled = true;
            loading.classList.add('active');

            // 显示用户消息
            addMessage(message, 'user');
            input.value = '';

            try {
                const response = await fetch('/web-chat/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        message: message,
                        session_id: sessionId  // 传递会话ID
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    let content = data.message;
                    
                    // 如果有工具调用，显示工具信息
                    if (data.tool_calls && data.tool_calls.length > 0) {
                        const toolInfo = `<div class="tool-call">🔧 调用了工具: ${data.tool_calls.join(', ')}</div>`;
                        content += toolInfo;
                    }
                    
                    addMessage(content, 'assistant');
                } else {
                    addMessage('❌ ' + (data.message || '处理失败，请重试'), 'assistant');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('❌ 网络错误，请检查连接后重试', 'assistant');
            } finally {
                // 恢复输入
                input.disabled = false;
                sendBtn.disabled = false;
                loading.classList.remove('active');
                input.focus();
            }
        }

        function addMessage(content, role) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            // 如果是 AI 回复，使用 Markdown 渲染
            if (role === 'assistant' && typeof marked !== 'undefined') {
                try {
                    // 配置 marked
                    marked.setOptions({
                        breaks: true,  // 支持换行
                        gfm: true      // GitHub Flavored Markdown
                    });
                    
                    // 分离工具调用信息和正文
                    let mainContent = content;
                    let toolCallHtml = '';
                    
                    // 提取工具调用信息
                    const toolCallMatch = content.match(/<div class="tool-call">.*?<\/div>/);
                    if (toolCallMatch) {
                        toolCallHtml = toolCallMatch[0];
                        mainContent = content.replace(toolCallMatch[0], '');
                    }
                    
                    // 渲染 Markdown
                    contentDiv.innerHTML = marked.parse(mainContent) + toolCallHtml;
                } catch (e) {
                    console.error('Markdown 解析失败:', e);
                    contentDiv.innerHTML = content.replace(/\n/g, '<br>');
                }
            } else {
                // 用户消息或系统消息保持原样
                if (role === 'user') {
                    contentDiv.textContent = content;
                } else {
                    contentDiv.innerHTML = content;
                }
            }
            
            messageDiv.appendChild(contentDiv);
            messagesDiv.appendChild(messageDiv);
            
            // 滚动到底部
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function addSystemMessage(content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            messagesDiv.appendChild(messageDiv);
            
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 切换示例面板
        function toggleExamples() {
            const panel = document.getElementById('examplesPanel');
            panel.classList.toggle('hidden');
        }

        // 使用示例提示词
        function useExample(text) {
            input.value = text;
            input.focus();
            // 可选：自动发送
            // sendMessage();
        }

        // 页面加载时显示欢迎提示
        window.addEventListener('load', () => {
            setTimeout(() => {
                addSystemMessage('💡 点击右上角的灯泡图标查看提示词示例');
            }, 1000);
        });
    </script>
</body>
</html>
