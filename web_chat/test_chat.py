"""
Web Chat 功能测试脚本
"""
import asyncio
import httpx


async def test_chat_api():
    """测试聊天 API"""
    print("🧪 测试 Web Chat API...")
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 测试 1: 获取工具列表
        print("\n1️⃣ 测试获取工具列表...")
        try:
            response = await client.get(f"{base_url}/web-chat/api/tools")
            data = response.json()
            print(f"✅ 成功获取 {data.get('count', 0)} 个工具")
            if data.get('tools'):
                print(f"   示例工具: {data['tools'][0]['name']}")
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        # 测试 2: 发送简单问题
        print("\n2️⃣ 测试发送聊天消息...")
        try:
            response = await client.post(
                f"{base_url}/web-chat/api/chat",
                json={"message": "你好，请介绍一下你自己"}
            )
            data = response.json()
            if data.get('success'):
                print(f"✅ AI 回复: {data['message'][:100]}...")
                if data.get('tool_calls'):
                    print(f"   调用工具: {data['tool_calls']}")
            else:
                print(f"❌ 失败: {data.get('message')}")
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        # 测试 3: 测试工具调用（如果有合适的工具）
        print("\n3️⃣ 测试工具调用...")
        try:
            response = await client.post(
                f"{base_url}/web-chat/api/chat",
                json={"message": "帮我查询一下健康检查接口"}
            )
            data = response.json()
            if data.get('success'):
                print(f"✅ AI 回复: {data['message'][:100]}...")
                if data.get('tool_calls'):
                    print(f"   ✨ 成功调用工具: {data['tool_calls']}")
                else:
                    print(f"   ℹ️ 未调用工具（可能不需要）")
            else:
                print(f"❌ 失败: {data.get('message')}")
        except Exception as e:
            print(f"❌ 失败: {e}")
    
    print("\n✅ 测试完成！")


if __name__ == "__main__":
    print("=" * 50)
    print("  Web Chat 功能测试")
    print("=" * 50)
    print("\n⚠️ 请确保服务已启动: python main.py")
    print()
    
    try:
        asyncio.run(test_chat_api())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n\n❌ 测试失败: {e}")
