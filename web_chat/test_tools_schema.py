"""
测试工具 Schema 是否符合 OpenAI 规范
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web_chat.router import get_mcp_tools


async def test_tools_schema():
    """测试所有工具的 schema 是否有效"""
    print("=" * 60)
    print("测试工具 Schema 有效性")
    print("=" * 60)
    
    tools = await get_mcp_tools()
    
    print(f"\n✅ 成功获取 {len(tools)} 个工具\n")
    
    invalid_tools = []
    
    for i, tool in enumerate(tools, 1):
        function = tool.get("function", {})
        name = function.get("name", "unknown")
        parameters = function.get("parameters", {})
        
        print(f"{i}. 工具: {name}")
        print(f"   描述: {function.get('description', 'N/A')[:60]}...")
        
        # 检查 parameters 结构
        param_type = parameters.get("type")
        properties = parameters.get("properties", {})
        required = parameters.get("required", [])
        
        print(f"   参数类型: {param_type}")
        print(f"   参数数量: {len(properties)}")
        print(f"   必填参数: {len(required)}")
        
        # 验证是否符合 OpenAI 规范
        is_valid = True
        errors = []
        
        if param_type != "object":
            is_valid = False
            errors.append(f"参数 type 必须是 'object'，当前是 '{param_type}'")
        
        if not isinstance(properties, dict):
            is_valid = False
            errors.append(f"properties 必须是 dict，当前是 {type(properties)}")
        
        if not isinstance(required, list):
            is_valid = False
            errors.append(f"required 必须是 list，当前是 {type(required)}")
        
        if is_valid:
            print(f"   ✅ Schema 有效")
        else:
            print(f"   ❌ Schema 无效:")
            for error in errors:
                print(f"      - {error}")
            invalid_tools.append((name, errors))
        
        print()
    
    # 总结
    print("=" * 60)
    if invalid_tools:
        print(f"❌ 发现 {len(invalid_tools)} 个无效工具:")
        for name, errors in invalid_tools:
            print(f"\n  工具: {name}")
            for error in errors:
                print(f"    - {error}")
    else:
        print("✅ 所有工具 Schema 都有效！")
    print("=" * 60)
    
    # 输出一个示例工具的完整 JSON
    if tools:
        print("\n示例工具 JSON:")
        print(json.dumps(tools[0], indent=2, ensure_ascii=False))


if __name__ == "__main__":
    try:
        asyncio.run(test_tools_schema())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
