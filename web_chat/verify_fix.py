"""
快速验证修复是否成功
"""
import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web_chat.router import get_mcp_tools


async def verify_fix():
    """验证 Schema 修复是否成功"""
    print("=" * 60)
    print("🔍 验证 Schema 修复")
    print("=" * 60)
    
    tools = await get_mcp_tools()
    
    if not tools:
        print("❌ 无法获取工具列表")
        return False
    
    print(f"\n✅ 成功获取 {len(tools)} 个工具\n")
    
    # 检查所有工具的 Schema
    all_valid = True
    invalid_count = 0
    
    for tool in tools:
        function = tool.get("function", {})
        name = function.get("name", "unknown")
        parameters = function.get("parameters", {})
        
        param_type = parameters.get("type")
        properties = parameters.get("properties")
        required = parameters.get("required")
        
        # 验证关键字段
        if param_type != "object":
            print(f"❌ {name}: type 不是 'object' (当前: {param_type})")
            all_valid = False
            invalid_count += 1
        elif not isinstance(properties, dict):
            print(f"❌ {name}: properties 不是 dict (当前: {type(properties)})")
            all_valid = False
            invalid_count += 1
        elif not isinstance(required, list):
            print(f"❌ {name}: required 不是 list (当前: {type(required)})")
            all_valid = False
            invalid_count += 1
    
    print("=" * 60)
    if all_valid:
        print("✅ 修复验证成功！所有工具 Schema 都有效！")
        print(f"   - 工具总数: {len(tools)}")
        print(f"   - 有效工具: {len(tools)}")
        print(f"   - 无效工具: 0")
        print("\n🎉 可以正常使用 Web Chat 功能了！")
        print("\n下一步：")
        print("  1. 启动服务: python main.py")
        print("  2. 访问界面: http://localhost:8000/web-chat")
        print("  3. 开始聊天测试")
        return True
    else:
        print(f"❌ 修复验证失败！发现 {invalid_count} 个无效工具")
        print("\n请检查 web_chat/router.py 中的 get_mcp_tools() 函数")
        return False
    print("=" * 60)


if __name__ == "__main__":
    try:
        result = asyncio.run(verify_fix())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
