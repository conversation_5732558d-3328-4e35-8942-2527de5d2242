# 指定签署位置流程，用户签署时支持单页批量落章-测试用例

## 功能测试

### 配置项控制逻辑

#### TL-AppID配置不限时支持当前签署区当前页当前文档全部文档四种落章范围

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建多文档签署流程；个人甲为签署人；流程包含多个签署区分布在不同页面

##### 步骤一：个人甲进入签署页面，点击任意签署区的换章按钮

##### 步骤二：在印章弹窗中勾选"批量落章"选项

##### 步骤三：查看"应用到"下拉选项内容

##### 步骤四：依次选择"当前签署区"、"当前页"、"当前文档"、"全部文档"四个选项并确认批量落章

##### ER-预期结果：1：印章弹窗正确显示"批量落章"选项；2："应用到"下拉选项包含"当前签署区"、"当前页"、"当前文档"、"全部文档"四个选项；3：选择不同范围时批量落章功能正常执行；4：系统记住用户最后一次的选择

#### TL-AppID配置全部文档落章时支持当前签署区和全部文档两种范围

##### PD-前置条件：AppID的一键落章配置为全部文档落章；使用该AppID创建多文档签署流程；个人乙为签署人

##### 步骤一：个人乙进入签署页面，点击签署区的换章按钮

##### 步骤二：在印章弹窗中勾选"批量落章"选项

##### 步骤三：查看"应用到"下拉选项内容

##### ER-预期结果：1："应用到"下拉选项仅包含"当前签署区"和"全部文档"两个选项；2：不显示"当前页"和"当前文档"选项；3：批量落章功能按AppID配置限制正常工作

#### TL-AppID配置单文档落章时支持当前签署区和当前文档两种范围

##### PD-前置条件：AppID的一键落章配置为单文档落章；使用该AppID创建单文档签署流程；个人丙为签署人

##### 步骤一：个人丙进入签署页面，点击签署区的换章按钮

##### 步骤二：在印章弹窗中勾选"批量落章"选项

##### 步骤三：查看"应用到"下拉选项内容

##### ER-预期结果：1："应用到"下拉选项仅包含"当前签署区"和"当前文档"两个选项；2：不显示"当前页"和"全部文档"选项；3：AppID配置项正确控制可选范围

#### TL-AppID不允许一键落章时页面不展示落章范围选项

##### PD-前置条件：AppID的一键落章配置为不允许；使用该AppID创建签署流程；个人丁为签署人

##### 步骤一：个人丁进入签署页面，点击签署区的换章按钮

##### 步骤二：查看印章弹窗中的选项内容

##### ER-预期结果：1：印章弹窗中不显示"批量落章"选项；2：不展示任何落章范围选择；3：用户只能进行单个签署区的换章操作

### 骑缝章特殊处理逻辑

#### TL-AppID配置不限时骑缝章签署区批量落章不显示当前页选项

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建签署流程包含骑缝章签署区；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击骑缝章签署区的换章按钮

##### 步骤二：在印章弹窗中勾选"批量落章"选项

##### 步骤三：查看"应用到"下拉选项内容

##### ER-预期结果：1："应用到"下拉选项包含"当前签署区"、"当前文档"、"全部文档"选项；2：不显示"当前页"选项；3：骑缝章特殊处理逻辑正确隐藏当前页选项

#### TL-AppID配置不限时普通签署区和骑缝章签署区下拉选项动态调整

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建签署流程同时包含普通签署区和骑缝章签署区；个人乙为签署人

##### 步骤一：个人乙点击普通签署区的换章按钮，勾选批量落章，查看下拉选项

##### 步骤二：关闭印章弹窗，点击骑缝章签署区的换章按钮

##### 步骤三：在印章弹窗中勾选"批量落章"选项，查看下拉选项变化

##### ER-预期结果：1：普通签署区时下拉选项包含"当前签署区"、"当前页"、"当前文档"、"全部文档"；2：骑缝章签署区时下拉选项包含"当前签署区"、"当前文档"、"全部文档"；3：选项根据签署区类型动态调整

### 已盖章签署区覆盖逻辑验证

#### TL-AppID配置不限时批量落章会覆盖已盖章的签署区印章

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建签署流程，一页有3个同主体签署区；个人甲为签署人

##### 步骤一：个人甲点击第一个签署区，选择印章A，选择"当前签署区"完成盖章

##### 步骤二：个人甲点击第二个签署区的换章按钮，选择印章B

##### 步骤三：勾选"批量落章"，"应用到"选择"当前页"，点击确认

##### 步骤四：检查第一个签署区的印章变化

##### ER-预期结果：1：第一个签署区的印章A被替换为印章B；2：第二个和第三个签署区都盖上印章B；3：批量落章覆盖已有印章功能正常；4：与线上现有逻辑不同，需重点验证

#### TL-AppID配置不限时批量落章覆盖逻辑在当前文档范围的验证

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建多文档签署流程，文档A有5个同主体签署区，其中2个已盖印章A；个人甲为签署人

##### 步骤一：个人甲在文档A中点击未盖章签署区的换章按钮，选择印章B

##### 步骤二：勾选"批量落章"，"应用到"选择"当前文档"，点击确认

##### 步骤三：检查文档A中所有签署区的印章状态

##### ER-预期结果：1：文档A中已盖印章A的2个签署区被替换为印章B；2：文档A中未盖章的3个签署区都盖上印章B；3：文档A中所有5个签署区最终都是印章B；4：批量落章覆盖逻辑在文档范围正常工作

#### TL-AppID配置不限时批量落章覆盖逻辑在全部文档范围的验证

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建多文档签署流程，包含文档A、B、C，部分签署区已盖不同印章；个人甲为签署人

##### 步骤一：个人甲点击任意未盖章签署区的换章按钮，选择印章C

##### 步骤二：勾选"批量落章"，"应用到"选择"全部文档"，点击确认

##### 步骤三：检查所有文档中同主体签署区的印章状态

##### ER-预期结果：1：所有文档中已盖其他印章的同主体签署区都被替换为印章C；2：所有文档中未盖章的同主体签署区都盖上印章C；3：全部文档范围内同主体签署区最终都是印章C；4：跨文档批量覆盖功能正常

### 用户选择记忆功能

#### TL-AppID配置不限时用户在批量落章时系统记住上次选择的范围

##### PD-前置条件：AppID的一键落章配置为不限制；使用该AppID创建多文档签署流程；个人甲为签署人；个人甲之前选择过"当前文档"

##### 步骤一：个人甲进入签署页面，点击签署区的换章按钮

##### 步骤二：在印章弹窗中勾选"批量落章"选项

##### 步骤三：查看"应用到"下拉选项的默认选中值

##### 步骤四：选择"当前页"并执行批量落章

##### 步骤五：再次点击其他签署区的换章按钮，勾选批量落章查看默认选中值

##### ER-预期结果：1：首次打开时默认选中用户上次选择的"当前文档"；2：用户选择"当前页"后系统记录该选择；3：再次打开时默认选中"当前页"；4：用户偏好设置持久化保存

### 批量落章执行逻辑

#### TL-企业A选择当前签署区落章时仅对选中签署区生效

##### PD-前置条件：企业A配置为不限制；企业A创建签署流程包含多个签署区；个人甲为签署人

##### 步骤一：个人甲点击指定签署区的换章按钮

##### 步骤二：选择新印章，勾选"批量落章"，"应用到"选择"当前签署区"

##### 步骤三：点击确认执行批量落章

##### 步骤四：检查各签署区的印章变化情况

##### ER-预期结果：1：仅选中的签署区更换为新选择的印章；2：其他签署区印章保持不变；3：当前签署区选项功能正常

#### TL-企业A选择当前页批量落章时对当前页所有同主体签署区生效

##### PD-前置条件：企业A配置为不限制；企业A创建签署流程包含3页文档，第1页有2个签署区，第2页有3个同主体签署区，第3页有1个签署区；个人甲为签署人；当前查看第2页

##### 步骤一：个人甲在第2页点击任意签署区的换章按钮

##### 步骤二：选择新印章，勾选"批量落章"，"应用到"选择"当前页"

##### 步骤三：点击确认执行批量落章

##### 步骤四：检查各页签署区的印章变化情况

##### ER-预期结果：1：第2页的3个同主体签署区全部更换为新选择的印章；2：第1页和第3页的签署区印章保持不变；3：批量落章仅作用于当前页面的同主体签署区

#### TL-企业A选择当前文档批量落章时对整个文档所有同主体签署区生效

##### PD-前置条件：企业A配置为不限制；企业A创建多文档签署流程，文档A有5个同主体签署区分布在3页，文档B有3个签署区分布在2页；个人甲为签署人；当前查看文档A

##### 步骤一：个人甲在文档A中点击任意签署区的换章按钮

##### 步骤二：选择新印章，勾选"批量落章"，"应用到"选择"当前文档"

##### 步骤三：点击确认执行批量落章

##### 步骤四：检查文档A和文档B中签署区的印章变化情况

##### ER-预期结果：1：文档A的5个同主体签署区全部更换为新选择的印章；2：文档B的签署区印章保持不变；3：批量落章仅作用于当前文档的同主体签署区

#### TL-企业A选择全部文档批量落章时对流程中所有同主体签署区生效

##### PD-前置条件：企业A配置为不限制；企业A创建多文档签署流程，包含文档A、文档B、文档C，总共15个同主体签署区；个人甲为签署人

##### 步骤一：个人甲点击任意文档中任意签署区的换章按钮

##### 步骤二：选择新印章，勾选"批量落章"，"应用到"选择"全部文档"

##### 步骤三：点击确认执行批量落章

##### 步骤四：逐一检查所有文档中签署区的印章变化情况

##### ER-预期结果：1：文档A、文档B、文档C中的所有15个同主体签署区全部更换为新选择的印章；2：批量落章作用于整个签署流程的所有同主体签署区；3：跨文档批量落章功能正常执行

## 界面交互测试

### 印章弹窗界面优化

#### TL-企业A印章弹窗中一键落章文本正确显示为批量落章

##### PD-前置条件：企业A配置项oneClick值为2；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击签署区的换章按钮

##### 步骤二：查看印章弹窗中的相关文本显示

##### ER-预期结果：1：印章弹窗中显示"批量落章"而不是"一键落章"；2：相关提示文本和按钮文案使用"批量落章"术语；3：界面文案与产品需求保持一致

#### TL-企业A通过换章方式打开的印章弹窗不显示应用到选项

##### PD-前置条件：企业A配置项oneClick值为2；个人甲为签署人；签署流程包含多个签署区

##### 步骤一：个人甲点击已有印章的签署区，选择"换章"功能

##### 步骤二：在换章印章弹窗中查看界面元素

##### ER-预期结果：1：换章弹窗中不显示"应用到"下拉选项；2：换章弹窗中不显示批量落章相关选项；3：换章功能仅针对当前选中的签署区生效

### 模板编辑页配置项

#### TL-企业A模板编辑页批量落章开关配置正确显示

##### PD-前置条件：企业A具有模板编辑权限；企业A进入模板编辑页面

##### 步骤一：企业A在模板编辑页面点击右上角"更多"按钮

##### 步骤二：查看批量落章相关配置项

##### 步骤三：查看配置项的标题和说明内容

##### ER-预期结果：1：配置项标题显示为"批量落章"而不是"一键落章"；2：配置说明内容为"开启后则允许签署时在指定范围内所有签署区批量落章。根据相关法律规定，金融类合同文件禁止使用此功能，请您确认本合同的类型并选择合适的配置。"；3：配置项文案与产品需求一致

## 冒烟用例

### 核心功能冒烟验证

#### TL-企业A配置不限时批量落章基本功能正常

##### PD-前置条件：企业A配置为不限制；创建包含多个同主体签署区的签署流程；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击签署区换章按钮

##### 步骤二：勾选"批量落章"，选择"当前页"，点击确认

##### ER-预期结果：1：印章弹窗正常显示批量落章选项；2：当前页所有同主体签署区成功批量更换印章；3：批量落章核心功能正常运行

#### TL-企业A批量落章覆盖已盖章签署区功能验证

##### PD-前置条件：企业A配置为不限制；创建签署流程，一页有3个同主体签署区，第一个已盖印章A；个人甲为签署人

##### 步骤一：个人甲点击第二个签署区换章按钮，选择印章B

##### 步骤二：勾选"批量落章"，选择"当前页"，点击确认

##### ER-预期结果：1：第一个签署区的印章A被替换为印章B；2：所有签署区最终都是印章B；3：批量落章覆盖已有印章功能正常

#### TL-企业B配置单文档落章时选项正确限制

##### PD-前置条件：企业B配置为单文档落章；创建签署流程；个人乙为签署人

##### 步骤一：个人乙点击签署区换章按钮

##### 步骤二：勾选"批量落章"，查看"应用到"选项

##### ER-预期结果：1：下拉选项仅包含"当前签署区"和"当前文档"；2：配置项正确控制可选范围；3：系统按配置限制功能正常

#### TL-企业C不允许一键落章时不显示批量落章选项

##### PD-前置条件：企业C配置为不允许一键落章；创建签署流程；个人丙为签署人

##### 步骤一：个人丙点击签署区换章按钮

##### 步骤二：查看印章弹窗选项

##### ER-预期结果：1：印章弹窗中不显示"批量落章"选项；2：配置项正确控制功能显示；3：不允许配置下功能正确隐藏

## 异常处理测试

### 配置项异常处理

#### TL-企业A配置项获取失败时批量落章功能降级处理

##### PD-前置条件：企业A的配置项接口v1/openplatform/getAppConfigByAppId返回异常；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击签署区换章按钮

##### 步骤二：在印章弹窗中查看批量落章相关选项

##### 步骤三：尝试勾选批量落章并查看可选范围

##### ER-预期结果：1：系统使用默认配置值进行降级处理；2：批量落章功能仍可正常使用；3：用户体验不受严重影响；4：系统记录异常日志便于排查

#### TL-企业A配置项值异常时系统默认处理逻辑

##### PD-前置条件：企业A的配置项返回非预期值（如异常字符串、null等）；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击签署区换章按钮

##### 步骤二：勾选"批量落章"查看"应用到"选项

##### ER-预期结果：1：系统按默认配置处理异常值；2：下拉选项显示合理的默认范围选项；3：批量落章功能正常可用；4：异常值不影响核心功能

### H5端交互验证

#### TL-企业A在H5端批量落章交互体验验证

##### PD-前置条件：企业A配置项oneClick值为2；个人甲使用手机浏览器进入签署页面

##### 步骤一：个人甲在H5端点击签署区换章按钮

##### 步骤二：在移动端印章弹窗中勾选"批量落章"

##### 步骤三：通过下拉选择器选择"当前页"并确认

##### ER-预期结果：1：H5端印章弹窗正确显示批量落章选项；2：下拉选择器在移动端交互友好；3：批量落章功能在H5端正常执行；4：移动端界面适配良好

### 性能边界测试

#### TL-企业A大量签署区场景下批量落章性能验证

##### PD-前置条件：企业A配置项oneClick值为2；创建包含50个以上签署区的大型签署流程；个人甲为签署人

##### 步骤一：个人甲进入签署页面，点击任意签署区换章按钮

##### 步骤二：选择新印章，勾选"批量落章"，选择"全部文档"

##### 步骤三：点击确认执行批量落章，观察处理时间和界面响应

##### ER-预期结果：1：批量落章处理时间在可接受范围内（5秒以内）；2：界面在处理过程中保持响应；3：所有签署区成功批量更换印章；4：大量数据场景下功能稳定可靠
